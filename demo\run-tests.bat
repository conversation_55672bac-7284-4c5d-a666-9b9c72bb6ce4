@echo off
echo Starting Cinema Booking Backend Tests...
echo.

echo Cleaning and compiling the project...
call mvn clean compile

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo Compilation successful! Running tests...
call mvn test

if %ERRORLEVEL% neq 0 (
    echo Tests failed!
    pause
    exit /b 1
)

echo.
echo All tests passed successfully!
echo.
echo To run the application:
echo mvn spring-boot:run
echo.
pause
