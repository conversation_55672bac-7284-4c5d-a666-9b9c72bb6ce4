package com.cinema.booking.repository;

import com.cinema.booking.model.Booking;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookingRepository extends JpaRepository<Booking, Long> {
    
    /**
     * Find booking by reference number
     */
    Optional<Booking> findByBookingReference(String bookingReference);
    
    /**
     * Find bookings by user ID
     */
    List<Booking> findByUserId(Long userId);
    
    /**
     * Find bookings by user ID with pagination
     */
    Page<Booking> findByUserId(Long userId, Pageable pageable);
    
    /**
     * Find bookings by showtime ID
     */
    List<Booking> findByShowtimeId(Long showtimeId);
    
    /**
     * Find bookings by status
     */
    List<Booking> findByBookingStatus(Booking.BookingStatus status);
    
    /**
     * Find bookings by user and status
     */
    List<Booking> findByUserIdAndBookingStatus(Long userId, Booking.BookingStatus status);
    
    /**
     * Find bookings by customer email
     */
    List<Booking> findByCustomerEmail(String customerEmail);
    
    /**
     * Find bookings within date range
     */
    @Query("SELECT b FROM Booking b WHERE b.bookingDate BETWEEN :startDate AND :endDate")
    List<Booking> findByBookingDateBetween(@Param("startDate") LocalDateTime startDate, 
                                          @Param("endDate") LocalDateTime endDate);
    
    /**
     * Find recent bookings for a user
     */
    @Query("SELECT b FROM Booking b WHERE b.user.id = :userId AND b.bookingDate >= :fromDate ORDER BY b.bookingDate DESC")
    List<Booking> findRecentBookingsByUser(@Param("userId") Long userId, 
                                          @Param("fromDate") LocalDateTime fromDate);
    
    /**
     * Find upcoming bookings for a user
     */
    @Query("SELECT b FROM Booking b WHERE b.user.id = :userId AND b.showtime.showDate >= :currentDate " +
           "AND b.bookingStatus != 'CANCELLED' ORDER BY b.showtime.showDate, b.showtime.showTime")
    List<Booking> findUpcomingBookingsByUser(@Param("userId") Long userId, 
                                            @Param("currentDate") java.time.LocalDate currentDate);
    
    /**
     * Find bookings for a specific movie
     */
    @Query("SELECT b FROM Booking b WHERE b.showtime.movie.id = :movieId")
    List<Booking> findByMovieId(@Param("movieId") Long movieId);
    
    /**
     * Find bookings for a specific theater
     */
    @Query("SELECT b FROM Booking b WHERE b.showtime.theater.id = :theaterId")
    List<Booking> findByTheaterId(@Param("theaterId") Long theaterId);
    
    /**
     * Count bookings by status
     */
    @Query("SELECT COUNT(b) FROM Booking b WHERE b.bookingStatus = :status")
    long countByStatus(@Param("status") Booking.BookingStatus status);
    
    /**
     * Count bookings for a user
     */
    @Query("SELECT COUNT(b) FROM Booking b WHERE b.user.id = :userId")
    long countByUserId(@Param("userId") Long userId);
    
    /**
     * Find bookings with total amount greater than specified value
     */
    @Query("SELECT b FROM Booking b WHERE b.totalAmount >= :minAmount")
    List<Booking> findByMinimumAmount(@Param("minAmount") java.math.BigDecimal minAmount);
    
    /**
     * Find top customers by booking count
     */
    @Query("SELECT b.user.id, COUNT(b) as bookingCount FROM Booking b " +
           "WHERE b.bookingStatus = 'CONFIRMED' " +
           "GROUP BY b.user.id ORDER BY bookingCount DESC")
    List<Object[]> findTopCustomersByBookingCount(Pageable pageable);
    
    /**
     * Find revenue by date range
     */
    @Query("SELECT SUM(b.totalAmount) FROM Booking b " +
           "WHERE b.bookingStatus = 'CONFIRMED' AND b.bookingDate BETWEEN :startDate AND :endDate")
    java.math.BigDecimal findRevenueByDateRange(@Param("startDate") LocalDateTime startDate, 
                                               @Param("endDate") LocalDateTime endDate);
    
    /**
     * Find bookings that can be cancelled (not in past and not cancelled)
     */
    @Query("SELECT b FROM Booking b WHERE b.bookingStatus != 'CANCELLED' " +
           "AND (b.showtime.showDate > :currentDate OR " +
           "(b.showtime.showDate = :currentDate AND b.showtime.showTime > :currentTime))")
    List<Booking> findCancellableBookings(@Param("currentDate") java.time.LocalDate currentDate,
                                         @Param("currentTime") java.time.LocalTime currentTime);
    
    /**
     * Find expired pending bookings
     */
    @Query("SELECT b FROM Booking b WHERE b.bookingStatus = 'PENDING' AND b.bookingDate < :expiryTime")
    List<Booking> findExpiredPendingBookings(@Param("expiryTime") LocalDateTime expiryTime);
    
    /**
     * Check if booking reference exists
     */
    boolean existsByBookingReference(String bookingReference);
}
