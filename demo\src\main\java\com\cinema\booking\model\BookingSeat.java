package com.cinema.booking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

@Entity
@Table(name = "booking_seats", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"booking_id", "seat_id"}))
public class BookingSeat {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "booking_id", nullable = false)
    @NotNull(message = "Booking is required")
    private Booking booking;
    
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "seat_id", nullable = false)
    @NotNull(message = "Seat is required")
    private Seat seat;
    
    @Column(nullable = false, precision = 10, scale = 2)
    @NotNull(message = "Price is required")
    private BigDecimal price;
    
    // Constructors
    public BookingSeat() {}
    
    public BookingSeat(Booking booking, Seat seat, BigDecimal price) {
        this.booking = booking;
        this.seat = seat;
        this.price = price;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Booking getBooking() {
        return booking;
    }
    
    public void setBooking(Booking booking) {
        this.booking = booking;
    }
    
    public Seat getSeat() {
        return seat;
    }
    
    public void setSeat(Seat seat) {
        this.seat = seat;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    // Utility methods
    public String getSeatLabel() {
        return seat != null ? seat.getSeatLabel() : "Unknown";
    }
    
    public String getSeatType() {
        return seat != null && seat.getSeatType() != null ? 
               seat.getSeatType().getDisplayName() : "Unknown";
    }
    
    public String getTheaterName() {
        return seat != null && seat.getTheater() != null ? 
               seat.getTheater().getName() : "Unknown Theater";
    }
    
    public String getFullDescription() {
        return String.format("Seat %s (%s) - $%.2f", 
                           getSeatLabel(), getSeatType(), price);
    }
}
