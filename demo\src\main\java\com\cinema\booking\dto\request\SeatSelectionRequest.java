package com.cinema.booking.dto.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

public class SeatSelectionRequest {
    
    @NotNull(message = "Showtime ID is required")
    private Long showtimeId;
    
    @NotEmpty(message = "At least one seat must be selected")
    @Size(max = 10, message = "Maximum 10 seats can be selected at once")
    private List<Long> seatIds;
    
    private String action; // "HOLD" or "RELEASE"
    
    // Constructors
    public SeatSelectionRequest() {}
    
    public SeatSelectionRequest(Long showtimeId, List<Long> seatIds, String action) {
        this.showtimeId = showtimeId;
        this.seatIds = seatIds;
        this.action = action;
    }
    
    // Getters and Setters
    public Long getShowtimeId() {
        return showtimeId;
    }
    
    public void setShowtimeId(Long showtimeId) {
        this.showtimeId = showtimeId;
    }
    
    public List<Long> getSeatIds() {
        return seatIds;
    }
    
    public void setSeatIds(List<Long> seatIds) {
        this.seatIds = seatIds;
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    // Utility methods
    public boolean isHoldAction() {
        return "HOLD".equalsIgnoreCase(action);
    }
    
    public boolean isReleaseAction() {
        return "RELEASE".equalsIgnoreCase(action);
    }
    
    public int getTotalSeats() {
        return seatIds != null ? seatIds.size() : 0;
    }
    
    @Override
    public String toString() {
        return "SeatSelectionRequest{" +
                "showtimeId=" + showtimeId +
                ", seatIds=" + seatIds +
                ", action='" + action + '\'' +
                '}';
    }
}
