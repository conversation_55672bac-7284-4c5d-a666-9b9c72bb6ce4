package com.cinema.booking.service;

import com.cinema.booking.dto.request.SeatSelectionRequest;
import com.cinema.booking.exception.BookingException;
import com.cinema.booking.exception.ResourceNotFoundException;
import com.cinema.booking.model.Seat;
import com.cinema.booking.model.Showtime;
import com.cinema.booking.repository.SeatRepository;
import com.cinema.booking.repository.ShowtimeRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@Transactional
public class SeatService {

    private static final Logger logger = LoggerFactory.getLogger(SeatService.class);

    @Autowired
    private SeatRepository seatRepository;

    @Autowired
    private ShowtimeRepository showtimeRepository;

    @Value("${app.booking.seat-hold-duration:300}") // 5 minutes in seconds
    private int seatHoldDuration;

    // In-memory seat hold management (in production, use Redis)
    private final Map<String, Long> heldSeats = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    public Seat createSeat(Seat seat) {
        logger.info("Creating new seat: {} in theater {}", seat.getSeatLabel(), seat.getTheater().getId());

        // Check if seat already exists in theater
        if (seatRepository.existsByTheaterIdAndRowNumberAndSeatNumber(
                seat.getTheater().getId(), seat.getRowNumber(), seat.getSeatNumber())) {
            throw new IllegalArgumentException("Seat " + seat.getSeatLabel() + " already exists in this theater");
        }

        Seat savedSeat = seatRepository.save(seat);
        logger.info("Seat created successfully with ID: {}", savedSeat.getId());
        
        return savedSeat;
    }

    public Seat getSeatById(Long id) {
        return seatRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Seat", "id", id));
    }

    public Seat updateSeat(Long id, Seat seatDetails) {
        logger.info("Updating seat with ID: {}", id);

        Seat seat = getSeatById(id);

        seat.setRowNumber(seatDetails.getRowNumber());
        seat.setSeatNumber(seatDetails.getSeatNumber());
        seat.setSeatType(seatDetails.getSeatType());
        seat.setPrice(seatDetails.getPrice());
        seat.setIsWheelchairAccessible(seatDetails.getIsWheelchairAccessible());

        Seat updatedSeat = seatRepository.save(seat);
        logger.info("Seat updated successfully: {}", updatedSeat.getId());
        
        return updatedSeat;
    }

    public void deleteSeat(Long id) {
        logger.info("Deleting seat with ID: {}", id);

        Seat seat = getSeatById(id);
        seatRepository.delete(seat);
        
        logger.info("Seat deleted successfully: {}", id);
    }

    @Cacheable(value = "theaterSeats", key = "#theaterId")
    public List<Seat> getSeatsByTheater(Long theaterId) {
        return seatRepository.findByTheaterIdOrderByRowNumberAscSeatNumberAsc(theaterId);
    }

    public List<Seat> getSeatsByTheaterAndType(Long theaterId, Seat.SeatType seatType) {
        return seatRepository.findByTheaterIdAndSeatType(theaterId, seatType);
    }

    public List<Seat> getWheelchairAccessibleSeats(Long theaterId) {
        return seatRepository.findByTheaterIdAndIsWheelchairAccessibleTrue(theaterId);
    }

    public List<Seat> getSeatsByRow(Long theaterId, String rowNumber) {
        return seatRepository.findByTheaterIdAndRowNumberOrderBySeatNumber(theaterId, rowNumber);
    }

    public List<Seat> getAvailableSeatsForShowtime(Long showtimeId) {
        Showtime showtime = showtimeRepository.findById(showtimeId)
                .orElseThrow(() -> new ResourceNotFoundException("Showtime", "id", showtimeId));

        List<Seat> availableSeats = seatRepository.findAvailableSeatsForShowtime(
                showtime.getTheater().getId(), showtimeId);

        // Filter out held seats
        return availableSeats.stream()
                .filter(seat -> !isSeatHeld(seat.getId(), showtimeId))
                .toList();
    }

    public List<Seat> getBookedSeatsForShowtime(Long showtimeId) {
        return seatRepository.findBookedSeatsForShowtime(showtimeId);
    }

    public boolean holdSeats(SeatSelectionRequest request, String userId) {
        logger.info("Holding seats for user: {} and showtime: {}", userId, request.getShowtimeId());

        // Validate showtime exists
        Showtime showtime = showtimeRepository.findById(request.getShowtimeId())
                .orElseThrow(() -> new ResourceNotFoundException("Showtime", "id", request.getShowtimeId()));

        // Check if seats are available
        if (!seatRepository.areSeatsAvailableForShowtime(request.getSeatIds(), request.getShowtimeId())) {
            throw BookingException.seatNotAvailable("One or more selected seats");
        }

        // Check if any seats are already held
        for (Long seatId : request.getSeatIds()) {
            if (isSeatHeld(seatId, request.getShowtimeId())) {
                throw BookingException.seatNotAvailable("Seat ID " + seatId);
            }
        }

        // Hold the seats
        for (Long seatId : request.getSeatIds()) {
            String holdKey = generateHoldKey(seatId, request.getShowtimeId(), userId);
            heldSeats.put(holdKey, System.currentTimeMillis());

            // Schedule automatic release
            scheduler.schedule(() -> releaseSeatHold(holdKey), seatHoldDuration, TimeUnit.SECONDS);
        }

        logger.info("Seats held successfully for user: {}", userId);
        return true;
    }

    public boolean releaseSeats(SeatSelectionRequest request, String userId) {
        logger.info("Releasing seats for user: {} and showtime: {}", userId, request.getShowtimeId());

        for (Long seatId : request.getSeatIds()) {
            String holdKey = generateHoldKey(seatId, request.getShowtimeId(), userId);
            heldSeats.remove(holdKey);
        }

        logger.info("Seats released successfully for user: {}", userId);
        return true;
    }

    public List<Seat> getSeatsByPriceRange(Long theaterId, BigDecimal minPrice, BigDecimal maxPrice) {
        return seatRepository.findByTheaterIdAndPriceRange(theaterId, minPrice, maxPrice);
    }

    public List<Seat> getPremiumSeats(Long theaterId) {
        return seatRepository.findPremiumSeatsByTheaterId(theaterId);
    }

    public List<String> getRowNumbers(Long theaterId) {
        return seatRepository.findDistinctRowNumbersByTheaterId(theaterId);
    }

    public long getSeatCount(Long theaterId) {
        return seatRepository.findByTheaterId(theaterId).size();
    }

    public long getSeatCountByType(Long theaterId, Seat.SeatType seatType) {
        return seatRepository.countByTheaterIdAndSeatType(theaterId, seatType);
    }

    public long getAvailableSeatCount(Long showtimeId) {
        Showtime showtime = showtimeRepository.findById(showtimeId)
                .orElseThrow(() -> new ResourceNotFoundException("Showtime", "id", showtimeId));

        return seatRepository.countAvailableSeatsForShowtime(showtime.getTheater().getId(), showtimeId);
    }

    // Private helper methods

    private boolean isSeatHeld(Long seatId, Long showtimeId) {
        return heldSeats.entrySet().stream()
                .anyMatch(entry -> entry.getKey().startsWith(seatId + "_" + showtimeId + "_"));
    }

    private String generateHoldKey(Long seatId, Long showtimeId, String userId) {
        return seatId + "_" + showtimeId + "_" + userId;
    }

    private void releaseSeatHold(String holdKey) {
        heldSeats.remove(holdKey);
        logger.debug("Automatically released seat hold: {}", holdKey);
    }

    // Utility methods for seat layout
    public Map<String, List<Seat>> getSeatLayoutByRow(Long theaterId) {
        List<Seat> seats = getSeatsByTheater(theaterId);
        return seats.stream()
                .collect(java.util.stream.Collectors.groupingBy(Seat::getRowNumber));
    }

    public boolean seatExists(Long theaterId, String rowNumber, Integer seatNumber) {
        return seatRepository.existsByTheaterIdAndRowNumberAndSeatNumber(theaterId, rowNumber, seatNumber);
    }

    // Cleanup method for expired holds
    public void cleanupExpiredHolds() {
        long currentTime = System.currentTimeMillis();
        long expiryTime = seatHoldDuration * 1000L; // Convert to milliseconds

        heldSeats.entrySet().removeIf(entry -> 
            (currentTime - entry.getValue()) > expiryTime
        );
    }
}
