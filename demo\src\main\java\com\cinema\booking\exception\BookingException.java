package com.cinema.booking.exception;

public class BookingException extends RuntimeException {
    
    private String errorCode;
    
    public BookingException(String message) {
        super(message);
    }
    
    public BookingException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public BookingException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public BookingException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    // Static factory methods for common booking errors
    public static BookingException seatNotAvailable(String seatLabel) {
        return new BookingException(
            String.format("Seat %s is not available for booking", seatLabel),
            "SEAT_NOT_AVAILABLE"
        );
    }
    
    public static BookingException showtimeNotAvailable(Long showtimeId) {
        return new BookingException(
            String.format("Showtime with ID %d is not available for booking", showtimeId),
            "SHOWTIME_NOT_AVAILABLE"
        );
    }
    
    public static BookingException insufficientSeats(int requested, int available) {
        return new BookingException(
            String.format("Insufficient seats available. Requested: %d, Available: %d", requested, available),
            "INSUFFICIENT_SEATS"
        );
    }
    
    public static BookingException bookingNotCancellable(String bookingReference) {
        return new BookingException(
            String.format("Booking %s cannot be cancelled", bookingReference),
            "BOOKING_NOT_CANCELLABLE"
        );
    }
    
    public static BookingException invalidBookingStatus(String currentStatus, String requiredStatus) {
        return new BookingException(
            String.format("Invalid booking status. Current: %s, Required: %s", currentStatus, requiredStatus),
            "INVALID_BOOKING_STATUS"
        );
    }
    
    public static BookingException maxSeatsExceeded(int maxSeats) {
        return new BookingException(
            String.format("Maximum %d seats can be booked at once", maxSeats),
            "MAX_SEATS_EXCEEDED"
        );
    }
    
    public static BookingException duplicateBookingReference(String bookingReference) {
        return new BookingException(
            String.format("Booking reference %s already exists", bookingReference),
            "DUPLICATE_BOOKING_REFERENCE"
        );
    }
}
