package com.cinema.booking.service;

import com.cinema.booking.exception.ResourceNotFoundException;
import com.cinema.booking.model.Movie;
import com.cinema.booking.model.Showtime;
import com.cinema.booking.model.Theater;
import com.cinema.booking.repository.MovieRepository;
import com.cinema.booking.repository.ShowtimeRepository;
import com.cinema.booking.repository.TheaterRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Service
@Transactional
public class ShowtimeService {

    private static final Logger logger = LoggerFactory.getLogger(ShowtimeService.class);

    @Autowired
    private ShowtimeRepository showtimeRepository;

    @Autowired
    private MovieRepository movieRepository;

    @Autowired
    private TheaterRepository theaterRepository;

    public Showtime createShowtime(Showtime showtime) {
        logger.info("Creating new showtime for movie: {} in theater: {}", 
                   showtime.getMovie().getId(), showtime.getTheater().getId());

        // Validate movie exists and is active
        Movie movie = movieRepository.findById(showtime.getMovie().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Movie", "id", showtime.getMovie().getId()));
        
        if (!movie.getIsActive()) {
            throw new IllegalArgumentException("Cannot create showtime for inactive movie");
        }

        // Validate theater exists
        Theater theater = theaterRepository.findById(showtime.getTheater().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Theater", "id", showtime.getTheater().getId()));

        // Check for scheduling conflicts
        validateNoSchedulingConflicts(showtime, movie.getDurationMinutes());

        // Set available seats to theater capacity
        showtime.setAvailableSeats(theater.getTotalSeats());

        Showtime savedShowtime = showtimeRepository.save(showtime);
        logger.info("Showtime created successfully with ID: {}", savedShowtime.getId());
        
        return savedShowtime;
    }

    public Showtime getShowtimeById(Long id) {
        return showtimeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Showtime", "id", id));
    }

    public Showtime updateShowtime(Long id, Showtime showtimeDetails) {
        logger.info("Updating showtime with ID: {}", id);

        Showtime showtime = getShowtimeById(id);

        // Validate if showtime can be updated (not in past, no bookings, etc.)
        if (showtime.isInPast()) {
            throw new IllegalStateException("Cannot update past showtime");
        }

        showtime.setShowDate(showtimeDetails.getShowDate());
        showtime.setShowTime(showtimeDetails.getShowTime());
        showtime.setBasePrice(showtimeDetails.getBasePrice());

        // If movie or theater changed, validate and update
        if (!showtime.getMovie().getId().equals(showtimeDetails.getMovie().getId())) {
            Movie newMovie = movieRepository.findById(showtimeDetails.getMovie().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Movie", "id", showtimeDetails.getMovie().getId()));
            showtime.setMovie(newMovie);
        }

        if (!showtime.getTheater().getId().equals(showtimeDetails.getTheater().getId())) {
            Theater newTheater = theaterRepository.findById(showtimeDetails.getTheater().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Theater", "id", showtimeDetails.getTheater().getId()));
            showtime.setTheater(newTheater);
            showtime.setAvailableSeats(newTheater.getTotalSeats());
        }

        Showtime updatedShowtime = showtimeRepository.save(showtime);
        logger.info("Showtime updated successfully: {}", updatedShowtime.getId());
        
        return updatedShowtime;
    }

    public void deleteShowtime(Long id) {
        logger.info("Deleting showtime with ID: {}", id);

        Showtime showtime = getShowtimeById(id);
        
        // Check if showtime has bookings
        if (!showtime.getBookings().isEmpty()) {
            throw new IllegalStateException("Cannot delete showtime with existing bookings");
        }

        showtimeRepository.delete(showtime);
        logger.info("Showtime deleted successfully: {}", id);
    }

    public List<Showtime> getShowtimesByMovie(Long movieId) {
        return showtimeRepository.findByMovieId(movieId);
    }

    public Page<Showtime> getShowtimesByMovie(Long movieId, Pageable pageable) {
        return showtimeRepository.findByMovieId(movieId, pageable);
    }

    public List<Showtime> getShowtimesByTheater(Long theaterId) {
        return showtimeRepository.findByTheaterId(theaterId);
    }

    public Page<Showtime> getShowtimesByTheater(Long theaterId, Pageable pageable) {
        return showtimeRepository.findByTheaterId(theaterId, pageable);
    }

    public List<Showtime> getShowtimesByDate(LocalDate date) {
        return showtimeRepository.findByShowDate(date);
    }

    public List<Showtime> getShowtimesByDateRange(LocalDate startDate, LocalDate endDate) {
        return showtimeRepository.findByShowDateBetween(startDate, endDate);
    }

    public List<Showtime> getShowtimesByMovieAndDate(Long movieId, LocalDate date) {
        return showtimeRepository.findByMovieIdAndShowDate(movieId, date);
    }

    public List<Showtime> getShowtimesByTheaterAndDate(Long theaterId, LocalDate date) {
        return showtimeRepository.findByTheaterIdAndShowDate(theaterId, date);
    }

    public List<Showtime> getUpcomingShowtimes() {
        LocalDate currentDate = LocalDate.now();
        LocalTime currentTime = LocalTime.now();
        return showtimeRepository.findUpcomingShowtimes(currentDate, currentTime);
    }

    public List<Showtime> getTodaysUpcomingShowtimes() {
        LocalDate today = LocalDate.now();
        LocalTime currentTime = LocalTime.now();
        return showtimeRepository.findTodaysUpcomingShowtimes(today, currentTime);
    }

    public List<Showtime> getShowtimesWithAvailableSeats() {
        return showtimeRepository.findShowtimesWithAvailableSeats();
    }

    public List<Showtime> getShowtimesByTimeRange(LocalDate date, LocalTime startTime, LocalTime endTime) {
        return showtimeRepository.findByDateAndTimeRange(date, startTime, endTime);
    }

    public List<Showtime> getPopularShowtimes(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return showtimeRepository.findPopularShowtimes(LocalDate.now().minusDays(30), pageable);
    }

    public List<Showtime> getShowtimesWithMinimumSeats(Integer minSeats) {
        return showtimeRepository.findShowtimesWithMinimumSeats(minSeats, LocalDate.now());
    }

    public List<Showtime> getShowtimesByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        return showtimeRepository.findByPriceRange(minPrice, maxPrice);
    }

    public long getShowtimeCount() {
        return showtimeRepository.count();
    }

    public long getShowtimeCountByMovie(Long movieId) {
        return showtimeRepository.countByMovieId(movieId);
    }

    public long getShowtimeCountByTheater(Long theaterId) {
        return showtimeRepository.countByTheaterId(theaterId);
    }

    // Utility methods

    public boolean isShowtimeAvailable(Long showtimeId) {
        Showtime showtime = getShowtimeById(showtimeId);
        return showtime.canBook();
    }

    public int getAvailableSeatsCount(Long showtimeId) {
        Showtime showtime = getShowtimeById(showtimeId);
        return showtime.getAvailableSeats();
    }

    public void updateAvailableSeats(Long showtimeId, int seatChange) {
        Showtime showtime = getShowtimeById(showtimeId);
        
        if (seatChange < 0) {
            showtime.decreaseAvailableSeats(Math.abs(seatChange));
        } else {
            showtime.increaseAvailableSeats(seatChange);
        }
        
        showtimeRepository.save(showtime);
    }

    // Private helper methods

    private void validateNoSchedulingConflicts(Showtime newShowtime, Integer movieDurationMinutes) {
        // Calculate end time for the new showtime
        LocalTime endTime = newShowtime.getShowTime().plusMinutes(movieDurationMinutes + 30); // 30 min buffer

        List<Showtime> conflictingShowtimes = showtimeRepository.findConflictingShowtimes(
                newShowtime.getTheater().getId(),
                newShowtime.getShowDate(),
                newShowtime.getShowTime(),
                endTime,
                movieDurationMinutes
        );

        if (!conflictingShowtimes.isEmpty()) {
            throw new IllegalArgumentException("Showtime conflicts with existing schedule");
        }
    }

    // Admin methods

    public Page<Showtime> getAllShowtimes(Pageable pageable) {
        return showtimeRepository.findAll(pageable);
    }

    public List<Showtime> getShowtimesForMovieInTheater(Long movieId, Long theaterId) {
        return showtimeRepository.findByMovieIdAndTheaterId(movieId, theaterId);
    }

    public void bulkUpdatePrices(List<Long> showtimeIds, BigDecimal newPrice) {
        logger.info("Bulk updating prices for {} showtimes", showtimeIds.size());

        for (Long showtimeId : showtimeIds) {
            try {
                Showtime showtime = getShowtimeById(showtimeId);
                showtime.setBasePrice(newPrice);
                showtimeRepository.save(showtime);
            } catch (Exception e) {
                logger.error("Failed to update price for showtime ID: {}", showtimeId, e);
            }
        }

        logger.info("Bulk price update completed");
    }
}
