package com.cinema.booking.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.License;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return ((OpenAPI) new OpenAPI())
                .info(new Info()
                        .title("Cinema Booking System API")
                        .description("RESTful API for Cinema Ticket Booking System with comprehensive theater management, movie integration via TMDB, user authentication, and booking functionality.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Cinema Booking Team")
                                .email("<EMAIL>")
                                .url("https://github.com/cinema-booking/backend"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")));
    }

    private static class OpenAPI {

        public OpenAPI() {
        }
    }
}




