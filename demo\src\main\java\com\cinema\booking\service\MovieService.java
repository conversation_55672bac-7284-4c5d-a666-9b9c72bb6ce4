package com.cinema.booking.service;

import com.cinema.booking.exception.ResourceNotFoundException;
import com.cinema.booking.model.Movie;
import com.cinema.booking.repository.MovieRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class MovieService {

    private static final Logger logger = LoggerFactory.getLogger(MovieService.class);

    @Autowired
    private MovieRepository movieRepository;

    public Movie createMovie(Movie movie) {
        logger.info("Creating new movie: {}", movie.getTitle());

        // Check if movie with same TMDB ID already exists
        if (movie.getTmdbId() != null && movieRepository.existsByTmdbId(movie.getTmdbId())) {
            throw new IllegalArgumentException("Movie with TMDB ID " + movie.getTmdbId() + " already exists");
        }

        Movie savedMovie = movieRepository.save(movie);
        logger.info("Movie created successfully with ID: {}", savedMovie.getId());
        
        return savedMovie;
    }

    public Movie getMovieById(Long id) {
        return movieRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Movie", "id", id));
    }

    public Optional<Movie> getMovieByTmdbId(Long tmdbId) {
        return movieRepository.findByTmdbId(tmdbId);
    }

    public Movie updateMovie(Long id, Movie movieDetails) {
        logger.info("Updating movie with ID: {}", id);

        Movie movie = getMovieById(id);

        movie.setTitle(movieDetails.getTitle());
        movie.setDescription(movieDetails.getDescription());
        movie.setGenre(movieDetails.getGenre());
        movie.setDurationMinutes(movieDetails.getDurationMinutes());
        movie.setRating(movieDetails.getRating());
        movie.setReleaseDate(movieDetails.getReleaseDate());
        movie.setPosterUrl(movieDetails.getPosterUrl());
        movie.setTrailerUrl(movieDetails.getTrailerUrl());
        movie.setLanguage(movieDetails.getLanguage());
        movie.setDirector(movieDetails.getDirector());
        movie.setCast(movieDetails.getCast());
        movie.setIsActive(movieDetails.getIsActive());

        Movie updatedMovie = movieRepository.save(movie);
        logger.info("Movie updated successfully: {}", updatedMovie.getId());
        
        return updatedMovie;
    }

    public void deleteMovie(Long id) {
        logger.info("Deleting movie with ID: {}", id);

        Movie movie = getMovieById(id);
        movieRepository.delete(movie);
        
        logger.info("Movie deleted successfully: {}", id);
    }

    public void deactivateMovie(Long id) {
        logger.info("Deactivating movie with ID: {}", id);

        Movie movie = getMovieById(id);
        movie.setIsActive(false);
        movieRepository.save(movie);
        
        logger.info("Movie deactivated successfully: {}", id);
    }

    public void activateMovie(Long id) {
        logger.info("Activating movie with ID: {}", id);

        Movie movie = getMovieById(id);
        movie.setIsActive(true);
        movieRepository.save(movie);
        
        logger.info("Movie activated successfully: {}", id);
    }

    public List<Movie> getAllActiveMovies() {
        return movieRepository.findByIsActiveTrue();
    }

    public Page<Movie> getAllActiveMovies(Pageable pageable) {
        return movieRepository.findByIsActiveTrue(pageable);
    }

    public Page<Movie> getAllMovies(Pageable pageable) {
        return movieRepository.findAll(pageable);
    }

    public List<Movie> getMoviesByGenre(String genre) {
        return movieRepository.findByGenreAndIsActiveTrue(genre);
    }

    public Page<Movie> searchMovies(String searchTerm, Pageable pageable) {
        return movieRepository.searchMovies(searchTerm, pageable);
    }

    public Page<Movie> searchMoviesByTitle(String title, Pageable pageable) {
        return movieRepository.searchByTitle(title, pageable);
    }

    public List<Movie> getCurrentlyPlayingMovies() {
        return movieRepository.findCurrentlyPlaying(LocalDate.now());
    }

    public List<Movie> getUpcomingMovies() {
        return movieRepository.findUpcoming(LocalDate.now());
    }

    public List<Movie> getFeaturedMovies(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return movieRepository.findFeaturedMovies(pageable);
    }

    public List<Movie> getMoviesWithShowtimes() {
        return movieRepository.findMoviesWithShowtimes(LocalDate.now());
    }

    public List<String> getAllGenres() {
        return movieRepository.findAllGenres();
    }

    public List<Movie> getMoviesByRating(String rating) {
        return movieRepository.findByRatingAndIsActiveTrue(rating);
    }

    public List<Movie> getMoviesByDurationRange(Integer minDuration, Integer maxDuration) {
        return movieRepository.findByDurationRange(minDuration, maxDuration);
    }

    public List<Movie> getMoviesByReleaseDateRange(LocalDate startDate, LocalDate endDate) {
        return movieRepository.findByReleaseDateBetween(startDate, endDate);
    }

    public long getMovieCount() {
        return movieRepository.count();
    }

    public long getActiveMovieCount() {
        return movieRepository.findByIsActiveTrue().size();
    }

    public long getMovieCountByGenre(String genre) {
        return movieRepository.countByGenre(genre);
    }

    public boolean existsByTmdbId(Long tmdbId) {
        return movieRepository.existsByTmdbId(tmdbId);
    }

    // Utility method for pagination with default sorting
    public Page<Movie> getMoviesWithPagination(int page, int size, String sortBy, String sortDir) {
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        return getAllActiveMovies(pageable);
    }

    // Search with filters
    public Page<Movie> searchMoviesWithFilters(String searchTerm, String genre, String rating, 
                                              Integer minDuration, Integer maxDuration, 
                                              Pageable pageable) {
        // This would require a custom repository method with Criteria API
        // For now, we'll use the basic search
        return searchMovies(searchTerm, pageable);
    }
}
