# Cinema Booking Backend Configuration
spring.application.name=cinema-booking-backend

# Server Configuration
server.port=8080
server.servlet.context-path=/api

# Database Configuration - Using H2 for demo
spring.datasource.url=jdbc:h2:mem:cinema_booking;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console (for testing)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.defer-datasource-initialization=true

# SQL Initialization
spring.sql.init.mode=always
spring.sql.init.data-locations=classpath:data.sql
spring.sql.init.continue-on-error=true

# JWT Configuration
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000
jwt.refresh-expiration=604800000
