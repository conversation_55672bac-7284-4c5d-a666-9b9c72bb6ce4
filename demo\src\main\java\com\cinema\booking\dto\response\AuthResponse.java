package com.cinema.booking.dto.response;

import com.cinema.booking.model.User;

public class AuthResponse {
    
    private String token;
    private String refreshToken;
    private String type = "Bearer";
    private Long id;
    private String email;
    private String firstName;
    private String lastName;
    private String role;
    private String preferredGenre;
    private Long preferredTheaterId;
    
    // Constructors
    public AuthResponse() {}
    
    public AuthResponse(String token, String refreshToken, User user) {
        this.token = token;
        this.refreshToken = refreshToken;
        this.id = user.getId();
        this.email = user.getEmail();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.role = user.getRole().name();
        this.preferredGenre = user.getPreferredGenre();
        this.preferredTheaterId = user.getPreferredTheater() != null ? 
                                 user.getPreferredTheater().getId() : null;
    }
    
    // Getters and Setters
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public String getRefreshToken() {
        return refreshToken;
    }
    
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getFirstName() {
        return firstName;
    }
    
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    
    public String getLastName() {
        return lastName;
    }
    
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getPreferredGenre() {
        return preferredGenre;
    }
    
    public void setPreferredGenre(String preferredGenre) {
        this.preferredGenre = preferredGenre;
    }
    
    public Long getPreferredTheaterId() {
        return preferredTheaterId;
    }
    
    public void setPreferredTheaterId(Long preferredTheaterId) {
        this.preferredTheaterId = preferredTheaterId;
    }
    
    // Utility methods
    public String getFullName() {
        return firstName + " " + lastName;
    }
    
    public boolean isAdmin() {
        return "ADMIN".equals(role);
    }
}
