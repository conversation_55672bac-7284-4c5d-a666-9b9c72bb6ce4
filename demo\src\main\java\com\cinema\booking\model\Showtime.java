package com.cinema.booking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Entity
@Table(name = "showtimes")
public class Showtime {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "movie_id", nullable = false)
    @NotNull(message = "Movie is required")
    private Movie movie;
    
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "theater_id", nullable = false)
    @NotNull(message = "Theater is required")
    private Theater theater;
    
    @Column(name = "show_date", nullable = false)
    @NotNull(message = "Show date is required")
    private LocalDate showDate;
    
    @Column(name = "show_time", nullable = false)
    @NotNull(message = "Show time is required")
    private LocalTime showTime;
    
    @Column(name = "base_price", nullable = false, precision = 10, scale = 2)
    @NotNull(message = "Base price is required")
    private BigDecimal basePrice;
    
    @Column(name = "available_seats", nullable = false)
    @NotNull(message = "Available seats is required")
    @PositiveOrZero(message = "Available seats must be zero or positive")
    private Integer availableSeats;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @OneToMany(mappedBy = "showtime", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Booking> bookings;
    
    // Constructors
    public Showtime() {
        this.createdAt = LocalDateTime.now();
    }
    
    public Showtime(Movie movie, Theater theater, LocalDate showDate, LocalTime showTime, BigDecimal basePrice) {
        this();
        this.movie = movie;
        this.theater = theater;
        this.showDate = showDate;
        this.showTime = showTime;
        this.basePrice = basePrice;
        this.availableSeats = theater != null ? theater.getTotalSeats() : 0;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Movie getMovie() {
        return movie;
    }
    
    public void setMovie(Movie movie) {
        this.movie = movie;
    }
    
    public Theater getTheater() {
        return theater;
    }
    
    public void setTheater(Theater theater) {
        this.theater = theater;
    }
    
    public LocalDate getShowDate() {
        return showDate;
    }
    
    public void setShowDate(LocalDate showDate) {
        this.showDate = showDate;
    }
    
    public LocalTime getShowTime() {
        return showTime;
    }
    
    public void setShowTime(LocalTime showTime) {
        this.showTime = showTime;
    }
    
    public BigDecimal getBasePrice() {
        return basePrice;
    }
    
    public void setBasePrice(BigDecimal basePrice) {
        this.basePrice = basePrice;
    }
    
    public Integer getAvailableSeats() {
        return availableSeats;
    }
    
    public void setAvailableSeats(Integer availableSeats) {
        this.availableSeats = availableSeats;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public List<Booking> getBookings() {
        return bookings;
    }
    
    public void setBookings(List<Booking> bookings) {
        this.bookings = bookings;
    }
    
    // Utility methods
    public LocalDateTime getShowDateTime() {
        return LocalDateTime.of(showDate, showTime);
    }
    
    public boolean isInPast() {
        return getShowDateTime().isBefore(LocalDateTime.now());
    }
    
    public boolean isToday() {
        return showDate.equals(LocalDate.now());
    }
    
    public boolean hasAvailableSeats() {
        return availableSeats > 0;
    }
    
    public boolean canBook() {
        return !isInPast() && hasAvailableSeats();
    }
    
    public void decreaseAvailableSeats(int count) {
        if (availableSeats >= count) {
            this.availableSeats -= count;
        } else {
            throw new IllegalStateException("Not enough available seats");
        }
    }
    
    public void increaseAvailableSeats(int count) {
        this.availableSeats += count;
    }
    
    public String getDisplayTime() {
        return showTime.toString();
    }
    
    public String getDisplayDate() {
        return showDate.toString();
    }
    
    public String getFullDisplayInfo() {
        return String.format("%s - %s at %s (%s)", 
            movie != null ? movie.getTitle() : "Unknown Movie",
            theater != null ? theater.getName() : "Unknown Theater",
            getDisplayTime(),
            getDisplayDate()
        );
    }
}
