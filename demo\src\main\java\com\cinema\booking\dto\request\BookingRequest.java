package com.cinema.booking.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

public class BookingRequest {
    
    @NotNull(message = "Showtime ID is required")
    private Long showtimeId;
    
    @NotEmpty(message = "At least one seat must be selected")
    @Size(max = 10, message = "Maximum 10 seats can be booked at once")
    private List<Long> seatIds;
    
    @NotBlank(message = "Customer name is required")
    @Size(max = 255, message = "Customer name must not exceed 255 characters")
    private String customerName;
    
    @NotBlank(message = "Customer email is required")
    @Email(message = "Customer email should be valid")
    private String customerEmail;
    
    @Size(max = 20, message = "Customer phone must not exceed 20 characters")
    private String customerPhone;
    
    // Constructors
    public BookingRequest() {}
    
    public BookingRequest(Long showtimeId, List<Long> seatIds, String customerName, String customerEmail) {
        this.showtimeId = showtimeId;
        this.seatIds = seatIds;
        this.customerName = customerName;
        this.customerEmail = customerEmail;
    }
    
    // Getters and Setters
    public Long getShowtimeId() {
        return showtimeId;
    }
    
    public void setShowtimeId(Long showtimeId) {
        this.showtimeId = showtimeId;
    }
    
    public List<Long> getSeatIds() {
        return seatIds;
    }
    
    public void setSeatIds(List<Long> seatIds) {
        this.seatIds = seatIds;
    }
    
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getCustomerEmail() {
        return customerEmail;
    }
    
    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }
    
    public String getCustomerPhone() {
        return customerPhone;
    }
    
    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }
    
    // Utility methods
    public int getTotalSeats() {
        return seatIds != null ? seatIds.size() : 0;
    }
    
    @Override
    public String toString() {
        return "BookingRequest{" +
                "showtimeId=" + showtimeId +
                ", seatIds=" + seatIds +
                ", customerName='" + customerName + '\'' +
                ", customerEmail='" + customerEmail + '\'' +
                ", customerPhone='" + customerPhone + '\'' +
                '}';
    }
}
