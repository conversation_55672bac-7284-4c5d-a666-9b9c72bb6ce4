package com.cinema.booking.service;

import com.cinema.booking.config.JwtConfig;
import com.cinema.booking.dto.request.LoginRequest;
import com.cinema.booking.dto.request.RegisterRequest;
import com.cinema.booking.dto.response.AuthResponse;
import com.cinema.booking.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthService.class);

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtConfig jwtConfig;

    public AuthResponse login(LoginRequest loginRequest) {
        logger.info("Attempting login for email: {}", loginRequest.getEmail());

        try {
            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getEmail(),
                    loginRequest.getPassword()
                )
            );

            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            User user = userService.getUserByEmail(userDetails.getUsername());

            // Generate tokens
            String token = jwtConfig.generateToken(userDetails.getUsername());
            String refreshToken = jwtConfig.generateRefreshToken(userDetails.getUsername());

            logger.info("Login successful for user: {}", user.getEmail());

            return new AuthResponse(token, refreshToken, user);

        } catch (AuthenticationException e) {
            logger.error("Login failed for email: {}", loginRequest.getEmail());
            throw new BadCredentialsException("Invalid email or password");
        }
    }

    public AuthResponse register(RegisterRequest registerRequest) {
        logger.info("Attempting registration for email: {}", registerRequest.getEmail());

        // Create user
        User user = userService.createUser(registerRequest);

        // Generate tokens
        String token = jwtConfig.generateToken(user.getEmail());
        String refreshToken = jwtConfig.generateRefreshToken(user.getEmail());

        logger.info("Registration successful for user: {}", user.getEmail());

        return new AuthResponse(token, refreshToken, user);
    }

    public AuthResponse refreshToken(String refreshToken) {
        logger.info("Attempting token refresh");

        try {
            if (!jwtConfig.canTokenBeRefreshed(refreshToken)) {
                throw new IllegalArgumentException("Invalid refresh token");
            }

            String username = jwtConfig.extractUsername(refreshToken);
            User user = userService.getUserByEmail(username);

            // Generate new access token
            String newToken = jwtConfig.refreshToken(refreshToken);

            logger.info("Token refresh successful for user: {}", user.getEmail());

            AuthResponse response = new AuthResponse(newToken, refreshToken, user);
            return response;

        } catch (Exception e) {
            logger.error("Token refresh failed", e);
            throw new IllegalArgumentException("Invalid refresh token");
        }
    }

    public void logout(String token) {
        // In a production environment, you might want to:
        // 1. Add the token to a blacklist
        // 2. Store blacklisted tokens in Redis with expiration
        // 3. Check blacklist in JwtAuthenticationFilter

        logger.info("User logged out");
        // For now, we'll just log the logout
        // The frontend should remove the token from storage
    }

    public boolean validateToken(String token) {
        try {
            String username = jwtConfig.extractUsername(token);
            return jwtConfig.validateToken(token, username);
        } catch (Exception e) {
            logger.error("Token validation failed", e);
            return false;
        }
    }

    public String getUsernameFromToken(String token) {
        return jwtConfig.extractUsername(token);
    }

    public boolean isTokenExpired(String token) {
        try {
            return jwtConfig.extractExpiration(token).before(new java.util.Date());
        } catch (Exception e) {
            return true;
        }
    }
}
