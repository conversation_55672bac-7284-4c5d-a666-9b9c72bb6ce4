-- Initial data for Cinema Booking System

-- Insert Theaters
INSERT IGNORE INTO theaters (id, name, location, total_seats, screen_type) VALUES
(1, 'Grand Cinema Hall 1', 'Downtown Plaza, Screen 1', 120, '2D'),
(2, 'Grand Cinema Hall 2', 'Downtown Plaza, Screen 2', 150, '3D'),
(3, 'IMAX Theater', 'City Center Mall', 200, 'IMAX'),
(4, 'Cozy Cinema', 'Suburban Mall', 80, '2D');

-- Insert Seats for Theater 1 (Grand Cinema Hall 1) - 120 seats
INSERT IGNORE INTO seats (theater_id, row_number, seat_number, seat_type, price, is_wheelchair_accessible) VALUES
-- Row A (10 seats) - VIP
(1, 'A', 1, 'VIP', 25.00, false), (1, 'A', 2, 'VIP', 25.00, false), (1, 'A', 3, 'VIP', 25.00, false), (1, 'A', 4, 'VIP', 25.00, false), (1, 'A', 5, 'VIP', 25.00, false),
(1, 'A', 6, 'VIP', 25.00, false), (1, 'A', 7, 'VIP', 25.00, false), (1, 'A', 8, 'VIP', 25.00, false), (1, 'A', 9, 'VIP', 25.00, false), (1, 'A', 10, 'VIP', 25.00, false),
-- Row B (12 seats) - Premium
(1, 'B', 1, 'PREMIUM', 20.00, false), (1, 'B', 2, 'PREMIUM', 20.00, false), (1, 'B', 3, 'PREMIUM', 20.00, false), (1, 'B', 4, 'PREMIUM', 20.00, false), (1, 'B', 5, 'PREMIUM', 20.00, false), (1, 'B', 6, 'PREMIUM', 20.00, false),
(1, 'B', 7, 'PREMIUM', 20.00, false), (1, 'B', 8, 'PREMIUM', 20.00, false), (1, 'B', 9, 'PREMIUM', 20.00, false), (1, 'B', 10, 'PREMIUM', 20.00, false), (1, 'B', 11, 'PREMIUM', 20.00, false), (1, 'B', 12, 'PREMIUM', 20.00, false),
-- Rows C-H (Regular seats) - 98 seats total
(1, 'C', 1, 'REGULAR', 15.00, true), (1, 'C', 2, 'REGULAR', 15.00, false), (1, 'C', 3, 'REGULAR', 15.00, false), (1, 'C', 4, 'REGULAR', 15.00, false), (1, 'C', 5, 'REGULAR', 15.00, false), (1, 'C', 6, 'REGULAR', 15.00, false),
(1, 'C', 7, 'REGULAR', 15.00, false), (1, 'C', 8, 'REGULAR', 15.00, false), (1, 'C', 9, 'REGULAR', 15.00, false), (1, 'C', 10, 'REGULAR', 15.00, false), (1, 'C', 11, 'REGULAR', 15.00, false), (1, 'C', 12, 'REGULAR', 15.00, false),
(1, 'C', 13, 'REGULAR', 15.00, false), (1, 'C', 14, 'REGULAR', 15.00, false), (1, 'C', 15, 'REGULAR', 15.00, false), (1, 'C', 16, 'REGULAR', 15.00, true);

-- Insert sample movies
INSERT IGNORE INTO movies (id, title, description, genre, duration_minutes, rating, release_date, poster_url, language, director, cast, is_active, tmdb_id) VALUES
(1, 'The Dark Knight', 'When the menace known as the Joker wreaks havoc and chaos on the people of Gotham, Batman must accept one of the greatest psychological and physical tests of his ability to fight injustice.', 'Action', 152, 'PG-13', '2008-07-18', 'https://image.tmdb.org/t/p/w500/qJ2tW6WMUDux911r6m7haRef0WH.jpg', 'English', 'Christopher Nolan', 'Christian Bale, Heath Ledger, Aaron Eckhart', true, 155),
(2, 'Inception', 'A thief who steals corporate secrets through the use of dream-sharing technology is given the inverse task of planting an idea into the mind of a C.E.O.', 'Sci-Fi', 148, 'PG-13', '2010-07-16', 'https://image.tmdb.org/t/p/w500/9gk7adHYeDvHkCSEqAvQNLV5Uge.jpg', 'English', 'Christopher Nolan', 'Leonardo DiCaprio, Marion Cotillard, Tom Hardy', true, 27205),
(3, 'Avengers: Endgame', 'After the devastating events of Avengers: Infinity War, the universe is in ruins due to the efforts of the Mad Titan, Thanos.', 'Action', 181, 'PG-13', '2019-04-26', 'https://image.tmdb.org/t/p/w500/or06FN3Dka5tukK1e9sl16pB3iy.jpg', 'English', 'Anthony Russo, Joe Russo', 'Robert Downey Jr., Chris Evans, Mark Ruffalo', true, 299534),
(4, 'Parasite', 'All unemployed, Ki-taeks family takes peculiar interest in the wealthy and glamorous Parks for their livelihood until they get entangled in an unexpected incident.', 'Thriller', 132, 'R', '2019-05-30', 'https://image.tmdb.org/t/p/w500/7IiTTgloJzvGI1TAYymCfbfl3vT.jpg', 'Korean', 'Bong Joon-ho', 'Song Kang-ho, Lee Sun-kyun, Cho Yeo-jeong', true, 496243);

-- Insert sample showtimes for today and next few days
INSERT IGNORE INTO showtimes (id, movie_id, theater_id, show_date, show_time, base_price, available_seats) VALUES
-- Today's showtimes
(1, 1, 1, CURDATE(), '14:00:00', 15.00, 120),
(2, 1, 1, CURDATE(), '17:30:00', 15.00, 120),
(3, 1, 1, CURDATE(), '21:00:00', 15.00, 120),
(4, 2, 2, CURDATE(), '15:00:00', 18.00, 150),
(5, 2, 2, CURDATE(), '18:30:00', 18.00, 150),
(6, 3, 3, CURDATE(), '16:00:00', 22.00, 200),
(7, 3, 3, CURDATE(), '20:00:00', 22.00, 200),
(8, 4, 4, CURDATE(), '19:00:00', 12.00, 80),

-- Tomorrow's showtimes
(9, 1, 1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '14:00:00', 15.00, 120),
(10, 1, 1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '17:30:00', 15.00, 120),
(11, 2, 2, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '15:00:00', 18.00, 150),
(12, 3, 3, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '16:00:00', 22.00, 200),
(13, 4, 4, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '19:00:00', 12.00, 80);

-- Insert a sample admin user (password: admin123)
INSERT IGNORE INTO users (id, email, password, first_name, last_name, phone, role, created_at) VALUES
(1, '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'Admin', 'User', '+1234567890', 'ADMIN', NOW());

-- Insert a sample regular user (password: user123)
INSERT IGNORE INTO users (id, email, password, first_name, last_name, phone, role, preferred_genre, preferred_theater_id, created_at) VALUES
(2, '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'John', 'Doe', '+1987654321', 'USER', 'Action', 1, NOW());
