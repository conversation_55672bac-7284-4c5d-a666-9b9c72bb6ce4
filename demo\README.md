# Cinema Ticket Booking Backend

A comprehensive RESTful API for a cinema ticket booking system built with Spring Boot, featuring JWT authentication, movie management, seat booking, and TMDB integration.

## Features

### 🎬 Core Functionality
- **User Authentication & Authorization** - JWT-based authentication with role-based access control
- **Movie Management** - Complete movie catalog with TMDB API integration
- **Theater & Seat Management** - Multiple theaters with different seat types and pricing
- **Showtime Scheduling** - Flexible showtime management with availability tracking
- **Booking System** - Comprehensive booking workflow with seat selection and payment tracking
- **Real-time Seat Availability** - Live seat availability with temporary hold functionality

### 🔐 Security Features
- JWT token-based authentication
- Password encryption with BCrypt
- Role-based access control (USER, ADMIN)
- CORS configuration for frontend integration
- Comprehensive input validation

### 🎯 Business Logic
- Seat booking with conflict prevention
- Unique booking reference generation
- Dynamic pricing based on seat types
- User preferences management
- Booking cancellation with business rules

## Technology Stack

- **Framework**: Spring Boot 3.5.0
- **Security**: Spring Security with JWT
- **Database**: MySQL with JPA/Hibernate
- **Validation**: Bean Validation (Jakarta)
- **Documentation**: Comprehensive API documentation
- **Testing**: JUnit 5 with H2 in-memory database
- **External APIs**: TMDB (The Movie Database) integration

## Project Structure

```
src/main/java/com/cinema/booking/
├── CinemaBookingApplication.java          # Main application class
├── config/                                # Configuration classes
│   ├── SecurityConfig.java               # Spring Security configuration
│   ├── JwtConfig.java                     # JWT token configuration
│   └── CorsConfig.java                    # CORS configuration
├── controller/                            # REST controllers
│   ├── AuthController.java               # Authentication endpoints
│   ├── UserController.java               # User management
│   ├── MovieController.java              # Movie operations
│   ├── TheaterController.java            # Theater management
│   ├── ShowtimeController.java           # Showtime operations
│   ├── BookingController.java            # Booking management
│   └── SeatController.java               # Seat operations
├── service/                               # Business logic layer
│   ├── AuthService.java                  # Authentication services
│   ├── UserService.java                  # User operations
│   ├── MovieService.java                 # Movie management
│   ├── TheaterService.java               # Theater operations
│   ├── ShowtimeService.java              # Showtime management
│   ├── BookingService.java               # Booking operations
│   └── SeatService.java                  # Seat management
├── repository/                            # Data access layer
│   ├── UserRepository.java               # User data access
│   ├── MovieRepository.java              # Movie data access
│   ├── TheaterRepository.java            # Theater data access
│   ├── ShowtimeRepository.java           # Showtime data access
│   ├── BookingRepository.java            # Booking data access
│   └── SeatRepository.java               # Seat data access
├── model/                                 # JPA entities
│   ├── User.java                         # User entity
│   ├── Movie.java                        # Movie entity
│   ├── Theater.java                      # Theater entity
│   ├── Showtime.java                     # Showtime entity
│   ├── Booking.java                      # Booking entity
│   ├── Seat.java                         # Seat entity
│   └── BookingSeat.java                  # Booking-Seat relationship
├── dto/                                   # Data Transfer Objects
│   ├── request/                          # Request DTOs
│   └── response/                         # Response DTOs
├── exception/                             # Exception handling
│   ├── GlobalExceptionHandler.java       # Global exception handler
│   ├── ResourceNotFoundException.java    # Custom exceptions
│   └── BookingException.java
└── security/                              # Security components
    ├── JwtAuthenticationEntryPoint.java  # JWT entry point
    └── JwtAuthenticationFilter.java      # JWT filter
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh-token` - Refresh JWT token
- `POST /api/auth/logout` - User logout

### Movies
- `GET /api/movies` - Get all movies (paginated)
- `GET /api/movies/{id}` - Get movie by ID
- `GET /api/movies/search` - Search movies
- `GET /api/movies/genres` - Get all genres
- `GET /api/movies/featured` - Get featured movies
- `GET /api/movies/now-playing` - Get currently playing movies
- `GET /api/movies/upcoming` - Get upcoming movies

### Theaters
- `GET /api/theaters` - Get all theaters
- `GET /api/theaters/{id}` - Get theater details
- `GET /api/theaters/{id}/seats` - Get theater seat layout
- `GET /api/theaters/3d` - Get 3D theaters
- `GET /api/theaters/imax` - Get IMAX theaters

### Showtimes
- `GET /api/showtimes` - Get showtimes with filters
- `GET /api/showtimes/{id}` - Get showtime details
- `GET /api/showtimes/{id}/availability` - Get seat availability
- `GET /api/showtimes/movie/{movieId}` - Get showtimes for movie
- `GET /api/showtimes/upcoming` - Get upcoming showtimes

### Bookings
- `POST /api/bookings` - Create new booking
- `GET /api/bookings/{id}` - Get booking details
- `GET /api/bookings/reference/{ref}` - Get booking by reference
- `PUT /api/bookings/{id}/cancel` - Cancel booking
- `GET /api/bookings/user` - Get user's bookings

### Seats
- `GET /api/seats/showtime/{showtimeId}` - Get available seats
- `POST /api/seats/hold` - Hold seats temporarily
- `POST /api/seats/release` - Release held seats
- `GET /api/seats/theater/{theaterId}` - Get theater seats

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `PUT /api/users/preferences` - Update user preferences
- `PUT /api/users/password` - Update password

## Setup Instructions

### Prerequisites
- Java 17 or higher
- Maven 3.6+
- MySQL 8.0+
- TMDB API key (optional, for movie data integration)

### Database Setup
1. Create a MySQL database named `cinema_booking`
2. Update database credentials in `application.yml`

### Configuration
1. Clone the repository
2. Update `application.yml` with your database credentials:
```yaml
spring:
  datasource:
    url: ******************************************
    username: your_username
    password: your_password
```

3. Set environment variables (optional):
```bash
export DB_USERNAME=your_db_username
export DB_PASSWORD=your_db_password
export JWT_SECRET=your_jwt_secret_key
export TMDB_API_KEY=your_tmdb_api_key
```

### Running the Application
```bash
# Using Maven
mvn spring-boot:run

# Or using Maven wrapper
./mvnw spring-boot:run

# Or run the JAR file
mvn clean package
java -jar target/cinema-booking-backend-1.0.0.jar
```

The application will start on `http://localhost:8080/api`

### Testing
```bash
# Run all tests
mvn test

# Run with coverage
mvn test jacoco:report
```

## Sample Data

The application includes sample data that will be automatically loaded:
- 4 theaters with different configurations
- Sample movies with TMDB integration
- Seat layouts for each theater
- Sample showtimes
- Admin and test user accounts

### Default Users
- **Admin**: email: `<EMAIL>`, password: `admin123`
- **User**: email: `<EMAIL>`, password: `user123`

## API Documentation

### Authentication
All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### Response Format
All API responses follow a consistent format:
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... },
  "timestamp": "2024-01-01T12:00:00",
  "status": 200
}
```

### Error Handling
Errors are returned with appropriate HTTP status codes and descriptive messages:
```json
{
  "success": false,
  "message": "Error description",
  "timestamp": "2024-01-01T12:00:00",
  "status": 400,
  "path": "/api/endpoint"
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
