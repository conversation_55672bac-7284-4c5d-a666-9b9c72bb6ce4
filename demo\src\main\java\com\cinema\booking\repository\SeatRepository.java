package com.cinema.booking.repository;

import com.cinema.booking.model.Seat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SeatRepository extends JpaRepository<Seat, Long> {
    
    /**
     * Find all seats in a theater
     */
    List<Seat> findByTheaterId(Long theaterId);
    
    /**
     * Find seats by theater and seat type
     */
    List<Seat> findByTheaterIdAndSeatType(Long theaterId, Seat.SeatType seatType);
    
    /**
     * Find seat by theater, row, and seat number
     */
    Optional<Seat> findByTheaterIdAndRowNumberAndSeatNumber(Long theaterId, String rowNumber, Integer seatNumber);
    
    /**
     * Find wheelchair accessible seats in a theater
     */
    List<Seat> findByTheaterIdAndIsWheelchairAccessibleTrue(Long theaterId);
    
    /**
     * Find seats by row in a theater
     */
    List<Seat> findByTheaterIdAndRowNumberOrderBySeatNumber(Long theaterId, String rowNumber);
    
    /**
     * Find available seats for a specific showtime
     */
    @Query("SELECT s FROM Seat s WHERE s.theater.id = :theaterId AND s.id NOT IN " +
           "(SELECT bs.seat.id FROM BookingSeat bs JOIN bs.booking b " +
           "WHERE b.showtime.id = :showtimeId AND b.bookingStatus != 'CANCELLED')")
    List<Seat> findAvailableSeatsForShowtime(@Param("theaterId") Long theaterId, 
                                            @Param("showtimeId") Long showtimeId);
    
    /**
     * Find booked seats for a specific showtime
     */
    @Query("SELECT s FROM Seat s JOIN BookingSeat bs ON s.id = bs.seat.id " +
           "JOIN Booking b ON bs.booking.id = b.id " +
           "WHERE b.showtime.id = :showtimeId AND b.bookingStatus != 'CANCELLED'")
    List<Seat> findBookedSeatsForShowtime(@Param("showtimeId") Long showtimeId);
    
    /**
     * Count seats by theater and type
     */
    @Query("SELECT COUNT(s) FROM Seat s WHERE s.theater.id = :theaterId AND s.seatType = :seatType")
    long countByTheaterIdAndSeatType(@Param("theaterId") Long theaterId, 
                                    @Param("seatType") Seat.SeatType seatType);
    
    /**
     * Count available seats for a showtime
     */
    @Query("SELECT COUNT(s) FROM Seat s WHERE s.theater.id = :theaterId AND s.id NOT IN " +
           "(SELECT bs.seat.id FROM BookingSeat bs JOIN bs.booking b " +
           "WHERE b.showtime.id = :showtimeId AND b.bookingStatus != 'CANCELLED')")
    long countAvailableSeatsForShowtime(@Param("theaterId") Long theaterId, 
                                       @Param("showtimeId") Long showtimeId);
    
    /**
     * Find seats by price range in a theater
     */
    @Query("SELECT s FROM Seat s WHERE s.theater.id = :theaterId AND s.price BETWEEN :minPrice AND :maxPrice")
    List<Seat> findByTheaterIdAndPriceRange(@Param("theaterId") Long theaterId,
                                           @Param("minPrice") java.math.BigDecimal minPrice,
                                           @Param("maxPrice") java.math.BigDecimal maxPrice);
    
    /**
     * Find premium seats (VIP and Premium) in a theater
     */
    @Query("SELECT s FROM Seat s WHERE s.theater.id = :theaterId AND s.seatType IN ('PREMIUM', 'VIP')")
    List<Seat> findPremiumSeatsByTheaterId(@Param("theaterId") Long theaterId);
    
    /**
     * Find seats ordered by row and seat number
     */
    List<Seat> findByTheaterIdOrderByRowNumberAscSeatNumberAsc(Long theaterId);
    
    /**
     * Check if seat exists in theater
     */
    boolean existsByTheaterIdAndRowNumberAndSeatNumber(Long theaterId, String rowNumber, Integer seatNumber);
    
    /**
     * Find all distinct row numbers in a theater
     */
    @Query("SELECT DISTINCT s.rowNumber FROM Seat s WHERE s.theater.id = :theaterId ORDER BY s.rowNumber")
    List<String> findDistinctRowNumbersByTheaterId(@Param("theaterId") Long theaterId);
    
    /**
     * Find seats with specific IDs
     */
    @Query("SELECT s FROM Seat s WHERE s.id IN :seatIds")
    List<Seat> findBySeatIds(@Param("seatIds") List<Long> seatIds);
    
    /**
     * Check if seats are available for booking
     */
    @Query("SELECT CASE WHEN COUNT(bs) = 0 THEN true ELSE false END " +
           "FROM BookingSeat bs JOIN bs.booking b " +
           "WHERE bs.seat.id IN :seatIds AND b.showtime.id = :showtimeId AND b.bookingStatus != 'CANCELLED'")
    boolean areSeatsAvailableForShowtime(@Param("seatIds") List<Long> seatIds, 
                                        @Param("showtimeId") Long showtimeId);
}
