package com.cinema.booking.model;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;

@Entity
@Table(name = "theaters")
public class Theater {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    @NotBlank(message = "Theater name is required")
    @Size(max = 255, message = "Theater name must not exceed 255 characters")
    private String name;

    @Column()
    @Size(max = 255, message = "Location must not exceed 255 characters")
    private String location;

    @Column(name = "total_seats", nullable = false)
    @NotNull(message = "Total seats is required")
    @Positive(message = "Total seats must be positive")
    private Integer totalSeats;

    @Enumerated(EnumType.STRING)
    @Column(name = "screen_type", nullable = false)
    private ScreenType screenType = ScreenType.SCREEN_2D;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @OneToMany(mappedBy = "theater", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Seat> seats;

    @OneToMany(mappedBy = "theater", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Showtime> showtimes;

    @OneToMany(mappedBy = "preferredTheater", fetch = FetchType.LAZY)
    private List<User> preferredByUsers;

    public enum ScreenType {
        SCREEN_2D("2D"),
        SCREEN_3D("3D"),
        IMAX("IMAX");

        private final String displayName;

        ScreenType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // Constructors
    public Theater() {
        this.createdAt = LocalDateTime.now();
    }

    public Theater(String name, String location, Integer totalSeats, ScreenType screenType) {
        this();
        this.name = name;
        this.location = location;
        this.totalSeats = totalSeats;
        this.screenType = screenType;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getTotalSeats() {
        return totalSeats;
    }

    public void setTotalSeats(Integer totalSeats) {
        this.totalSeats = totalSeats;
    }

    public ScreenType getScreenType() {
        return screenType;
    }

    public void setScreenType(ScreenType screenType) {
        this.screenType = screenType;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public List<Seat> getSeats() {
        return seats;
    }

    public void setSeats(List<Seat> seats) {
        this.seats = seats;
    }

    public List<Showtime> getShowtimes() {
        return showtimes;
    }

    public void setShowtimes(List<Showtime> showtimes) {
        this.showtimes = showtimes;
    }

    public List<User> getPreferredByUsers() {
        return preferredByUsers;
    }

    public void setPreferredByUsers(List<User> preferredByUsers) {
        this.preferredByUsers = preferredByUsers;
    }

    // Utility methods
    public String getFullDisplayName() {
        return name + (location != null ? " - " + location : "");
    }

    public boolean supports3D() {
        return screenType == ScreenType.SCREEN_3D || screenType == ScreenType.IMAX;
    }

    public boolean isIMAX() {
        return screenType == ScreenType.IMAX;
    }
}
