package com.cinema.booking.controller;

import com.cinema.booking.dto.request.BookingRequest;
import com.cinema.booking.dto.response.ApiResponse;
import com.cinema.booking.dto.response.BookingResponse;
import com.cinema.booking.model.Booking;
import com.cinema.booking.model.User;
import com.cinema.booking.service.BookingService;
import com.cinema.booking.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/bookings")
@CrossOrigin(origins = "*", maxAge = 3600)
public class BookingController {

    private static final Logger logger = LoggerFactory.getLogger(BookingController.class);

    @Autowired
    private BookingService bookingService;

    @Autowired
    private UserService userService;

    @PostMapping
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BookingResponse>> createBooking(@Valid @RequestBody BookingRequest bookingRequest) {
        logger.info("Creating booking for showtime: {}", bookingRequest.getShowtimeId());

        try {
            // Get current user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            Booking booking = bookingService.createBooking(bookingRequest, currentUser);
            BookingResponse bookingResponse = new BookingResponse(booking);

            ApiResponse<BookingResponse> response = ApiResponse.success("Booking created successfully", bookingResponse);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            logger.error("Failed to create booking", e);
            ApiResponse<BookingResponse> response = ApiResponse.error("Failed to create booking: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BookingResponse>> getBookingById(@PathVariable Long id) {
        logger.info("Fetching booking with ID: {}", id);

        try {
            // Get current user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            Booking booking = bookingService.getBookingById(id);

            // Check if user owns the booking or is admin
            if (!booking.getUser().getId().equals(currentUser.getId()) && !currentUser.isAdmin()) {
                ApiResponse<BookingResponse> response = ApiResponse.error("Access denied to this booking");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            BookingResponse bookingResponse = new BookingResponse(booking);
            ApiResponse<BookingResponse> response = ApiResponse.success("Booking retrieved successfully", bookingResponse);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch booking with ID: {}", id, e);
            ApiResponse<BookingResponse> response = ApiResponse.error("Failed to fetch booking: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/reference/{reference}")
    public ResponseEntity<ApiResponse<BookingResponse>> getBookingByReference(@PathVariable String reference) {
        logger.info("Fetching booking with reference: {}", reference);

        try {
            Booking booking = bookingService.getBookingByReference(reference);
            BookingResponse bookingResponse = new BookingResponse(booking);

            ApiResponse<BookingResponse> response = ApiResponse.success("Booking retrieved successfully", bookingResponse);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch booking with reference: {}", reference, e);
            ApiResponse<BookingResponse> response = ApiResponse.error("Failed to fetch booking: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/user")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<BookingResponse>>> getUserBookings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "bookingDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        logger.info("Fetching user bookings - page: {}, size: {}", page, size);

        try {
            // Get current user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                       Sort.by(sortBy).descending() : 
                       Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            Page<Booking> bookings = bookingService.getBookingsByUser(currentUser.getId(), pageable);
            
            Page<BookingResponse> bookingResponses = bookings.map(BookingResponse::new);

            ApiResponse<Page<BookingResponse>> response = ApiResponse.success("User bookings retrieved successfully", bookingResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch user bookings", e);
            ApiResponse<Page<BookingResponse>> response = ApiResponse.error("Failed to fetch user bookings: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/user/upcoming")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<BookingResponse>>> getUpcomingUserBookings() {
        logger.info("Fetching upcoming user bookings");

        try {
            // Get current user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            List<Booking> bookings = bookingService.getUpcomingBookingsByUser(currentUser.getId());
            List<BookingResponse> bookingResponses = bookings.stream()
                    .map(BookingResponse::new)
                    .collect(Collectors.toList());

            ApiResponse<List<BookingResponse>> response = ApiResponse.success("Upcoming bookings retrieved successfully", bookingResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch upcoming user bookings", e);
            ApiResponse<List<BookingResponse>> response = ApiResponse.error("Failed to fetch upcoming bookings: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/{id}/confirm")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BookingResponse>> confirmBooking(@PathVariable Long id) {
        logger.info("Confirming booking with ID: {}", id);

        try {
            Booking booking = bookingService.confirmBooking(id);
            BookingResponse bookingResponse = new BookingResponse(booking);

            ApiResponse<BookingResponse> response = ApiResponse.success("Booking confirmed successfully", bookingResponse);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to confirm booking with ID: {}", id, e);
            ApiResponse<BookingResponse> response = ApiResponse.error("Failed to confirm booking: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/{id}/cancel")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BookingResponse>> cancelBooking(@PathVariable Long id) {
        logger.info("Cancelling booking with ID: {}", id);

        try {
            // Get current user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            Booking existingBooking = bookingService.getBookingById(id);

            // Check if user owns the booking or is admin
            if (!existingBooking.getUser().getId().equals(currentUser.getId()) && !currentUser.isAdmin()) {
                ApiResponse<BookingResponse> response = ApiResponse.error("Access denied to cancel this booking");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            Booking booking = bookingService.cancelBooking(id);
            BookingResponse bookingResponse = new BookingResponse(booking);

            ApiResponse<BookingResponse> response = ApiResponse.success("Booking cancelled successfully", bookingResponse);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to cancel booking with ID: {}", id, e);
            ApiResponse<BookingResponse> response = ApiResponse.error("Failed to cancel booking: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    // Admin endpoints

    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<BookingResponse>>> getAllBookings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "bookingDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        logger.info("Admin fetching all bookings - page: {}, size: {}", page, size);

        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                       Sort.by(sortBy).descending() : 
                       Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            Page<Booking> bookings = bookingService.getAllBookings(pageable);
            
            Page<BookingResponse> bookingResponses = bookings.map(BookingResponse::new);

            ApiResponse<Page<BookingResponse>> response = ApiResponse.success("All bookings retrieved successfully", bookingResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch all bookings", e);
            ApiResponse<Page<BookingResponse>> response = ApiResponse.error("Failed to fetch all bookings: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/admin/user/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<BookingResponse>>> getBookingsByUser(@PathVariable Long userId) {
        logger.info("Admin fetching bookings for user ID: {}", userId);

        try {
            List<Booking> bookings = bookingService.getBookingsByUser(userId);
            List<BookingResponse> bookingResponses = bookings.stream()
                    .map(BookingResponse::new)
                    .collect(Collectors.toList());

            ApiResponse<List<BookingResponse>> response = ApiResponse.success("User bookings retrieved successfully", bookingResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch bookings for user ID: {}", userId, e);
            ApiResponse<List<BookingResponse>> response = ApiResponse.error("Failed to fetch user bookings: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/admin/revenue")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BigDecimal>> getRevenue(
            @RequestParam String startDate,
            @RequestParam String endDate) {

        logger.info("Admin fetching revenue from {} to {}", startDate, endDate);

        try {
            LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
            LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");

            BigDecimal revenue = bookingService.getRevenueByDateRange(start, end);

            ApiResponse<BigDecimal> response = ApiResponse.success("Revenue retrieved successfully", revenue);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch revenue", e);
            ApiResponse<BigDecimal> response = ApiResponse.error("Failed to fetch revenue: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/admin/stats")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Object>> getBookingStats() {
        logger.info("Admin fetching booking statistics");

        try {
            long totalBookings = bookingService.getBookingCount();
            long confirmedBookings = bookingService.getBookingCountByStatus(Booking.BookingStatus.CONFIRMED);
            long pendingBookings = bookingService.getBookingCountByStatus(Booking.BookingStatus.PENDING);
            long cancelledBookings = bookingService.getBookingCountByStatus(Booking.BookingStatus.CANCELLED);

            Object stats = new Object() {
                public final long total = totalBookings;
                public final long confirmed = confirmedBookings;
                public final long pending = pendingBookings;
                public final long cancelled = cancelledBookings;
            };

            ApiResponse<Object> response = ApiResponse.success("Booking statistics retrieved successfully", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch booking statistics", e);
            ApiResponse<Object> response = ApiResponse.error("Failed to fetch booking statistics: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
