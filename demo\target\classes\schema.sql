-- Cinema Booking System Database Schema

-- Users Table
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('USER', 'ADMIN') DEFAULT 'USER',
    preferred_genre VARCHAR(100),
    preferred_theater_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Movies Table
CREATE TABLE IF NOT EXISTS movies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    genre VARCHAR(100),
    duration_minutes INT NOT NULL,
    rating VARCHAR(10),
    release_date DATE,
    poster_url VARCHAR(500),
    trailer_url VARCHAR(500),
    language VARCHAR(50),
    director <PERSON><PERSON><PERSON><PERSON>(255),
    cast TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    tmdb_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_movies_genre (genre),
    INDEX idx_movies_active (is_active),
    INDEX idx_movies_tmdb_id (tmdb_id)
);

-- Theaters Table
CREATE TABLE IF NOT EXISTS theaters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    total_seats INT NOT NULL,
    screen_type ENUM('2D', '3D', 'IMAX') DEFAULT '2D',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Seats Table
CREATE TABLE IF NOT EXISTS seats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    theater_id BIGINT NOT NULL,
    row_number CHAR(1) NOT NULL,
    seat_number INT NOT NULL,
    seat_type ENUM('REGULAR', 'PREMIUM', 'VIP') DEFAULT 'REGULAR',
    price DECIMAL(10,2) NOT NULL,
    is_wheelchair_accessible BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (theater_id) REFERENCES theaters(id) ON DELETE CASCADE,
    UNIQUE KEY unique_seat (theater_id, row_number, seat_number),
    INDEX idx_seats_theater (theater_id)
);

-- Showtimes Table
CREATE TABLE IF NOT EXISTS showtimes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    movie_id BIGINT NOT NULL,
    theater_id BIGINT NOT NULL,
    show_date DATE NOT NULL,
    show_time TIME NOT NULL,
    base_price DECIMAL(10,2) NOT NULL,
    available_seats INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    FOREIGN KEY (theater_id) REFERENCES theaters(id) ON DELETE CASCADE,
    INDEX idx_showtimes_movie (movie_id),
    INDEX idx_showtimes_theater (theater_id),
    INDEX idx_showtimes_date (show_date),
    INDEX idx_showtimes_datetime (show_date, show_time)
);

-- Bookings Table
CREATE TABLE IF NOT EXISTS bookings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    showtime_id BIGINT NOT NULL,
    booking_reference VARCHAR(20) UNIQUE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    booking_status ENUM('PENDING', 'CONFIRMED', 'CANCELLED') DEFAULT 'PENDING',
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20),
    booking_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (showtime_id) REFERENCES showtimes(id) ON DELETE CASCADE,
    INDEX idx_bookings_user (user_id),
    INDEX idx_bookings_showtime (showtime_id),
    INDEX idx_bookings_reference (booking_reference),
    INDEX idx_bookings_status (booking_status)
);

-- Booking_Seats Table
CREATE TABLE IF NOT EXISTS booking_seats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    booking_id BIGINT NOT NULL,
    seat_id BIGINT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (seat_id) REFERENCES seats(id) ON DELETE CASCADE,
    UNIQUE KEY unique_booking_seat (booking_id, seat_id),
    INDEX idx_booking_seats_booking (booking_id),
    INDEX idx_booking_seats_seat (seat_id)
);

-- Add foreign key constraint for preferred_theater_id in users table
ALTER TABLE users ADD CONSTRAINT fk_users_preferred_theater 
FOREIGN KEY (preferred_theater_id) REFERENCES theaters(id) ON DELETE SET NULL;
