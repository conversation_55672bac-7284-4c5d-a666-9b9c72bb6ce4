package com.cinema.booking.controller;

import com.cinema.booking.dto.response.ApiResponse;
import com.cinema.booking.model.Showtime;
import com.cinema.booking.service.ShowtimeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@RestController
@RequestMapping("/showtimes")
@CrossOrigin(origins = "*", maxAge = 3600)
public class ShowtimeController {

    private static final Logger logger = LoggerFactory.getLogger(ShowtimeController.class);

    @Autowired
    private ShowtimeService showtimeService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<Showtime>>> getAllShowtimes(
            @RequestParam(required = false) String date,
            @RequestParam(required = false) Long movieId,
            @RequestParam(required = false) Long theaterId) {
        
        logger.info("Fetching showtimes - date: {}, movieId: {}, theaterId: {}", date, movieId, theaterId);

        try {
            List<Showtime> showtimes;
            
            if (date != null && movieId != null) {
                LocalDate showDate = LocalDate.parse(date);
                showtimes = showtimeService.getShowtimesByMovieAndDate(movieId, showDate);
            } else if (date != null && theaterId != null) {
                LocalDate showDate = LocalDate.parse(date);
                showtimes = showtimeService.getShowtimesByTheaterAndDate(theaterId, showDate);
            } else if (movieId != null) {
                showtimes = showtimeService.getShowtimesByMovie(movieId);
            } else if (theaterId != null) {
                showtimes = showtimeService.getShowtimesByTheater(theaterId);
            } else if (date != null) {
                LocalDate showDate = LocalDate.parse(date);
                showtimes = showtimeService.getShowtimesByDate(showDate);
            } else {
                showtimes = showtimeService.getUpcomingShowtimes();
            }
            
            ApiResponse<List<Showtime>> response = ApiResponse.success("Showtimes retrieved successfully", showtimes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch showtimes", e);
            ApiResponse<List<Showtime>> response = ApiResponse.error("Failed to fetch showtimes: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Showtime>> getShowtimeById(@PathVariable Long id) {
        logger.info("Fetching showtime with ID: {}", id);

        try {
            Showtime showtime = showtimeService.getShowtimeById(id);
            ApiResponse<Showtime> response = ApiResponse.success("Showtime retrieved successfully", showtime);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch showtime with ID: {}", id, e);
            ApiResponse<Showtime> response = ApiResponse.error("Failed to fetch showtime: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}/availability")
    public ResponseEntity<ApiResponse<Object>> getShowtimeAvailability(@PathVariable Long id) {
        logger.info("Fetching availability for showtime ID: {}", id);

        try {
            Showtime showtime = showtimeService.getShowtimeById(id);
            
            Object availability = new Object() {
                public final Long showtimeId = showtime.getId();
                public final boolean isAvailable = showtime.canBook();
                public final int availableSeats = showtime.getAvailableSeats();
                public final int totalSeats = showtime.getTheater().getTotalSeats();
                public final BigDecimal basePrice = showtime.getBasePrice();
                public final String movieTitle = showtime.getMovie().getTitle();
                public final String theaterName = showtime.getTheater().getName();
                public final String showDate = showtime.getDisplayDate();
                public final String showTime = showtime.getDisplayTime();
            };
            
            ApiResponse<Object> response = ApiResponse.success("Showtime availability retrieved successfully", availability);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch showtime availability for ID: {}", id, e);
            ApiResponse<Object> response = ApiResponse.error("Failed to fetch showtime availability: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/movie/{movieId}")
    public ResponseEntity<ApiResponse<Page<Showtime>>> getShowtimesByMovie(
            @PathVariable Long movieId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "showDate") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        logger.info("Fetching showtimes for movie ID: {} - page: {}, size: {}", movieId, page, size);

        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                       Sort.by(sortBy).descending() : 
                       Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            Page<Showtime> showtimes = showtimeService.getShowtimesByMovie(movieId, pageable);
            
            ApiResponse<Page<Showtime>> response = ApiResponse.success("Showtimes for movie retrieved successfully", showtimes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch showtimes for movie ID: {}", movieId, e);
            ApiResponse<Page<Showtime>> response = ApiResponse.error("Failed to fetch showtimes for movie: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/theater/{theaterId}")
    public ResponseEntity<ApiResponse<Page<Showtime>>> getShowtimesByTheater(
            @PathVariable Long theaterId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "showDate") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        logger.info("Fetching showtimes for theater ID: {} - page: {}, size: {}", theaterId, page, size);

        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                       Sort.by(sortBy).descending() : 
                       Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            Page<Showtime> showtimes = showtimeService.getShowtimesByTheater(theaterId, pageable);
            
            ApiResponse<Page<Showtime>> response = ApiResponse.success("Showtimes for theater retrieved successfully", showtimes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch showtimes for theater ID: {}", theaterId, e);
            ApiResponse<Page<Showtime>> response = ApiResponse.error("Failed to fetch showtimes for theater: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/upcoming")
    public ResponseEntity<ApiResponse<List<Showtime>>> getUpcomingShowtimes() {
        logger.info("Fetching upcoming showtimes");

        try {
            List<Showtime> showtimes = showtimeService.getUpcomingShowtimes();
            ApiResponse<List<Showtime>> response = ApiResponse.success("Upcoming showtimes retrieved successfully", showtimes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch upcoming showtimes", e);
            ApiResponse<List<Showtime>> response = ApiResponse.error("Failed to fetch upcoming showtimes: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/today")
    public ResponseEntity<ApiResponse<List<Showtime>>> getTodaysShowtimes() {
        logger.info("Fetching today's showtimes");

        try {
            List<Showtime> showtimes = showtimeService.getTodaysUpcomingShowtimes();
            ApiResponse<List<Showtime>> response = ApiResponse.success("Today's showtimes retrieved successfully", showtimes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch today's showtimes", e);
            ApiResponse<List<Showtime>> response = ApiResponse.error("Failed to fetch today's showtimes: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/available")
    public ResponseEntity<ApiResponse<List<Showtime>>> getAvailableShowtimes() {
        logger.info("Fetching available showtimes");

        try {
            List<Showtime> showtimes = showtimeService.getShowtimesWithAvailableSeats();
            ApiResponse<List<Showtime>> response = ApiResponse.success("Available showtimes retrieved successfully", showtimes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch available showtimes", e);
            ApiResponse<List<Showtime>> response = ApiResponse.error("Failed to fetch available showtimes: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/popular")
    public ResponseEntity<ApiResponse<List<Showtime>>> getPopularShowtimes(
            @RequestParam(defaultValue = "10") int limit) {
        
        logger.info("Fetching popular showtimes with limit: {}", limit);

        try {
            List<Showtime> showtimes = showtimeService.getPopularShowtimes(limit);
            ApiResponse<List<Showtime>> response = ApiResponse.success("Popular showtimes retrieved successfully", showtimes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch popular showtimes", e);
            ApiResponse<List<Showtime>> response = ApiResponse.error("Failed to fetch popular showtimes: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/date-range")
    public ResponseEntity<ApiResponse<List<Showtime>>> getShowtimesByDateRange(
            @RequestParam String startDate,
            @RequestParam String endDate) {
        
        logger.info("Fetching showtimes by date range: {} to {}", startDate, endDate);

        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            List<Showtime> showtimes = showtimeService.getShowtimesByDateRange(start, end);
            ApiResponse<List<Showtime>> response = ApiResponse.success("Showtimes by date range retrieved successfully", showtimes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch showtimes by date range", e);
            ApiResponse<List<Showtime>> response = ApiResponse.error("Failed to fetch showtimes by date range: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/time-range")
    public ResponseEntity<ApiResponse<List<Showtime>>> getShowtimesByTimeRange(
            @RequestParam String date,
            @RequestParam String startTime,
            @RequestParam String endTime) {
        
        logger.info("Fetching showtimes by time range on {}: {} to {}", date, startTime, endTime);

        try {
            LocalDate showDate = LocalDate.parse(date);
            LocalTime start = LocalTime.parse(startTime);
            LocalTime end = LocalTime.parse(endTime);
            
            List<Showtime> showtimes = showtimeService.getShowtimesByTimeRange(showDate, start, end);
            ApiResponse<List<Showtime>> response = ApiResponse.success("Showtimes by time range retrieved successfully", showtimes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch showtimes by time range", e);
            ApiResponse<List<Showtime>> response = ApiResponse.error("Failed to fetch showtimes by time range: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
