package com.cinema.booking.controller;

import com.cinema.booking.dto.request.LoginRequest;
import com.cinema.booking.dto.request.RegisterRequest;
import com.cinema.booking.dto.response.ApiResponse;
import com.cinema.booking.dto.response.AuthResponse;
import com.cinema.booking.service.AuthService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    public ResponseEntity<ApiResponse<AuthResponse>> login(@Valid @RequestBody LoginRequest loginRequest) {
        logger.info("Login attempt for email: {}", loginRequest.getEmail());

        try {
            AuthResponse authResponse = authService.login(loginRequest);
            ApiResponse<AuthResponse> response = ApiResponse.success("Login successful", authResponse);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Login failed for email: {}", loginRequest.getEmail(), e);
            ApiResponse<AuthResponse> response = ApiResponse.error("Login failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
    }

    @PostMapping("/register")
    public ResponseEntity<ApiResponse<AuthResponse>> register(@Valid @RequestBody RegisterRequest registerRequest) {
        logger.info("Registration attempt for email: {}", registerRequest.getEmail());

        try {
            AuthResponse authResponse = authService.register(registerRequest);
            ApiResponse<AuthResponse> response = ApiResponse.success("Registration successful", authResponse);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            logger.error("Registration failed for email: {}", registerRequest.getEmail(), e);
            ApiResponse<AuthResponse> response = ApiResponse.error("Registration failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }

    @PostMapping("/refresh-token")
    public ResponseEntity<ApiResponse<AuthResponse>> refreshToken(@RequestBody String refreshToken) {
        logger.info("Token refresh attempt");

        try {
            AuthResponse authResponse = authService.refreshToken(refreshToken);
            ApiResponse<AuthResponse> response = ApiResponse.success("Token refreshed successfully", authResponse);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Token refresh failed", e);
            ApiResponse<AuthResponse> response = ApiResponse.error("Token refresh failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
    }

    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Void>> logout(@RequestHeader("Authorization") String token) {
        logger.info("Logout attempt");

        try {
            // Extract token from Bearer header
            String jwtToken = token.startsWith("Bearer ") ? token.substring(7) : token;
            authService.logout(jwtToken);
            
            ApiResponse<Void> response = ApiResponse.success("Logout successful");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Logout failed", e);
            ApiResponse<Void> response = ApiResponse.error("Logout failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }

    @GetMapping("/validate-token")
    public ResponseEntity<ApiResponse<Boolean>> validateToken(@RequestHeader("Authorization") String token) {
        try {
            String jwtToken = token.startsWith("Bearer ") ? token.substring(7) : token;
            boolean isValid = authService.validateToken(jwtToken);
            
            ApiResponse<Boolean> response = ApiResponse.success("Token validation result", isValid);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Token validation failed", e);
            ApiResponse<Boolean> response = ApiResponse.success("Token validation result", false);
            return ResponseEntity.ok(response);
        }
    }

    @GetMapping("/user-info")
    public ResponseEntity<ApiResponse<String>> getUserInfo(@RequestHeader("Authorization") String token) {
        try {
            String jwtToken = token.startsWith("Bearer ") ? token.substring(7) : token;
            String username = authService.getUsernameFromToken(jwtToken);
            
            ApiResponse<String> response = ApiResponse.success("User info retrieved", username);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to get user info", e);
            ApiResponse<String> response = ApiResponse.error("Failed to get user info: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
    }
}
