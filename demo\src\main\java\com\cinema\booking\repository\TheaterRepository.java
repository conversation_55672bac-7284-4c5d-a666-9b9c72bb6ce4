package com.cinema.booking.repository;

import com.cinema.booking.model.Theater;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TheaterRepository extends JpaRepository<Theater, Long> {

    /**
     * Find theaters by screen type
     */
    List<Theater> findByScreenType(Theater.ScreenType screenType);

    /**
     * Find theaters by location
     */
    @Query("SELECT t FROM Theater t WHERE LOWER(t.location) LIKE LOWER(CONCAT('%', :location, '%'))")
    List<Theater> findByLocationContaining(@Param("location") String location);

    /**
     * Find theaters by name
     */
    @Query("SELECT t FROM Theater t WHERE LOWER(t.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<Theater> findByNameContaining(@Param("name") String name);

    /**
     * Find theaters with minimum seat capacity
     */
    @Query("SELECT t FROM Theater t WHERE t.totalSeats >= :minSeats")
    List<Theater> findByMinimumSeats(@Param("minSeats") Integer minSeats);

    /**
     * Find theaters with seat capacity range
     */
    @Query("SELECT t FROM Theater t WHERE t.totalSeats BETWEEN :minSeats AND :maxSeats")
    List<Theater> findBySeatCapacityRange(@Param("minSeats") Integer minSeats,
                                         @Param("maxSeats") Integer maxSeats);

    /**
     * Find theaters with showtimes for a specific date
     */
    @Query("SELECT DISTINCT t FROM Theater t JOIN t.showtimes s WHERE s.showDate = :showDate")
    List<Theater> findTheatersWithShowtimesOnDate(@Param("showDate") java.time.LocalDate showDate);

    /**
     * Find theaters showing a specific movie
     */
    @Query("SELECT DISTINCT t FROM Theater t JOIN t.showtimes s WHERE s.movie.id = :movieId AND s.showDate >= :fromDate")
    List<Theater> findTheatersShowingMovie(@Param("movieId") Long movieId,
                                          @Param("fromDate") java.time.LocalDate fromDate);

    /**
     * Find theaters ordered by total seats (largest first)
     */
    List<Theater> findAllByOrderByTotalSeatsDesc();

    /**
     * Find theaters that support 3D
     */
    @Query("SELECT t FROM Theater t WHERE t.screenType IN ('SCREEN_3D', 'IMAX')")
    List<Theater> findTheatersSupporting3D();

    /**
     * Find IMAX theaters
     */
    @Query("SELECT t FROM Theater t WHERE t.screenType = 'IMAX'")
    List<Theater> findIMAXTheaters();

    /**
     * Count theaters by screen type
     */
    @Query("SELECT COUNT(t) FROM Theater t WHERE t.screenType = :screenType")
    long countByScreenType(@Param("screenType") Theater.ScreenType screenType);

    /**
     * Find theaters with available seats for a specific showtime
     */
    @Query("SELECT t FROM Theater t JOIN t.showtimes s WHERE s.id = :showtimeId AND s.availableSeats > 0")
    List<Theater> findTheatersWithAvailableSeats(@Param("showtimeId") Long showtimeId);
}
