package com.cinema.booking.service;

import com.cinema.booking.dto.request.BookingRequest;
import com.cinema.booking.exception.BookingException;
import com.cinema.booking.exception.ResourceNotFoundException;
import com.cinema.booking.model.*;
import com.cinema.booking.repository.BookingRepository;
import com.cinema.booking.repository.SeatRepository;
import com.cinema.booking.repository.ShowtimeRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Service
@Transactional
public class BookingService {

    private static final Logger logger = LoggerFactory.getLogger(BookingService.class);

    @Autowired
    private BookingRepository bookingRepository;

    @Autowired
    private ShowtimeRepository showtimeRepository;

    @Autowired
    private SeatRepository seatRepository;

    @Value("${app.booking.max-seats-per-booking:10}")
    private int maxSeatsPerBooking;

    @Value("${app.booking.booking-reference-length:8}")
    private int bookingReferenceLength;

    public Booking createBooking(BookingRequest bookingRequest, User user) {
        logger.info("Creating booking for user: {} and showtime: {}", user.getEmail(), bookingRequest.getShowtimeId());

        // Validate request
        validateBookingRequest(bookingRequest);

        // Get showtime
        Showtime showtime = showtimeRepository.findById(bookingRequest.getShowtimeId())
                .orElseThrow(() -> new ResourceNotFoundException("Showtime", "id", bookingRequest.getShowtimeId()));

        // Validate showtime availability
        if (!showtime.canBook()) {
            throw BookingException.showtimeNotAvailable(showtime.getId());
        }

        // Get and validate seats
        List<Seat> seats = validateAndGetSeats(bookingRequest.getSeatIds(), showtime);

        // Calculate total amount
        BigDecimal totalAmount = calculateTotalAmount(seats, showtime.getBasePrice());

        // Generate unique booking reference
        String bookingReference = generateBookingReference();

        // Create booking
        Booking booking = new Booking(user, showtime, bookingReference, totalAmount,
                bookingRequest.getCustomerName(), bookingRequest.getCustomerEmail());
        booking.setCustomerPhone(bookingRequest.getCustomerPhone());

        // Save booking first to get ID
        booking = bookingRepository.save(booking);

        // Create booking seats
        List<BookingSeat> bookingSeats = new ArrayList<>();
        for (Seat seat : seats) {
            BigDecimal seatPrice = seat.calculatePrice(showtime.getBasePrice());
            BookingSeat bookingSeat = new BookingSeat(booking, seat, seatPrice);
            bookingSeats.add(bookingSeat);
        }
        booking.setBookingSeats(bookingSeats);

        // Update showtime available seats
        showtime.decreaseAvailableSeats(seats.size());
        showtimeRepository.save(showtime);

        // Save final booking with seats
        booking = bookingRepository.save(booking);

        logger.info("Booking created successfully with reference: {}", bookingReference);
        return booking;
    }

    public Booking getBookingById(Long id) {
        return bookingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Booking", "id", id));
    }

    public Booking getBookingByReference(String bookingReference) {
        return bookingRepository.findByBookingReference(bookingReference)
                .orElseThrow(() -> new ResourceNotFoundException("Booking", "reference", bookingReference));
    }

    public List<Booking> getBookingsByUser(Long userId) {
        return bookingRepository.findByUserId(userId);
    }

    public Page<Booking> getBookingsByUser(Long userId, Pageable pageable) {
        return bookingRepository.findByUserId(userId, pageable);
    }

    public List<Booking> getUpcomingBookingsByUser(Long userId) {
        return bookingRepository.findUpcomingBookingsByUser(userId, java.time.LocalDate.now());
    }

    public Booking confirmBooking(Long bookingId) {
        logger.info("Confirming booking with ID: {}", bookingId);

        Booking booking = getBookingById(bookingId);

        if (!booking.isPending()) {
            throw BookingException.invalidBookingStatus(
                booking.getBookingStatus().name(), 
                Booking.BookingStatus.PENDING.name()
            );
        }

        booking.confirm();
        Booking confirmedBooking = bookingRepository.save(booking);

        logger.info("Booking confirmed successfully: {}", booking.getBookingReference());
        return confirmedBooking;
    }

    public Booking cancelBooking(Long bookingId) {
        logger.info("Cancelling booking with ID: {}", bookingId);

        Booking booking = getBookingById(bookingId);

        if (!booking.canBeCancelled()) {
            throw BookingException.bookingNotCancellable(booking.getBookingReference());
        }

        // Release seats back to showtime
        Showtime showtime = booking.getShowtime();
        showtime.increaseAvailableSeats(booking.getTotalSeats());
        showtimeRepository.save(showtime);

        booking.cancel();
        Booking cancelledBooking = bookingRepository.save(booking);

        logger.info("Booking cancelled successfully: {}", booking.getBookingReference());
        return cancelledBooking;
    }

    public List<Booking> getBookingsByShowtime(Long showtimeId) {
        return bookingRepository.findByShowtimeId(showtimeId);
    }

    public List<Booking> getBookingsByStatus(Booking.BookingStatus status) {
        return bookingRepository.findByBookingStatus(status);
    }

    public BigDecimal getRevenueByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        BigDecimal revenue = bookingRepository.findRevenueByDateRange(startDate, endDate);
        return revenue != null ? revenue : BigDecimal.ZERO;
    }

    public long getBookingCount() {
        return bookingRepository.count();
    }

    public long getBookingCountByStatus(Booking.BookingStatus status) {
        return bookingRepository.countByStatus(status);
    }

    // Private helper methods

    private void validateBookingRequest(BookingRequest request) {
        if (request.getSeatIds().size() > maxSeatsPerBooking) {
            throw BookingException.maxSeatsExceeded(maxSeatsPerBooking);
        }
    }

    private List<Seat> validateAndGetSeats(List<Long> seatIds, Showtime showtime) {
        // Get seats
        List<Seat> seats = seatRepository.findBySeatIds(seatIds);

        if (seats.size() != seatIds.size()) {
            throw new ResourceNotFoundException("One or more seats not found");
        }

        // Validate all seats belong to the same theater as showtime
        for (Seat seat : seats) {
            if (!seat.getTheater().getId().equals(showtime.getTheater().getId())) {
                throw new IllegalArgumentException("Seat " + seat.getSeatLabel() + " does not belong to the showtime theater");
            }
        }

        // Check seat availability
        if (!seatRepository.areSeatsAvailableForShowtime(seatIds, showtime.getId())) {
            throw BookingException.seatNotAvailable("One or more selected seats");
        }

        return seats;
    }

    private BigDecimal calculateTotalAmount(List<Seat> seats, BigDecimal basePrice) {
        return seats.stream()
                .map(seat -> seat.calculatePrice(basePrice))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private String generateBookingReference() {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder reference = new StringBuilder();

        do {
            reference.setLength(0);
            for (int i = 0; i < bookingReferenceLength; i++) {
                reference.append(characters.charAt(random.nextInt(characters.length())));
            }
        } while (bookingRepository.existsByBookingReference(reference.toString()));

        return reference.toString();
    }

    // Admin methods
    public Page<Booking> getAllBookings(Pageable pageable) {
        return bookingRepository.findAll(pageable);
    }

    public List<Object[]> getTopCustomers(int limit) {
        return bookingRepository.findTopCustomersByBookingCount(
            org.springframework.data.domain.PageRequest.of(0, limit)
        );
    }

    public void cleanupExpiredPendingBookings() {
        logger.info("Cleaning up expired pending bookings");

        LocalDateTime expiryTime = LocalDateTime.now().minusMinutes(30); // 30 minutes expiry
        List<Booking> expiredBookings = bookingRepository.findExpiredPendingBookings(expiryTime);

        for (Booking booking : expiredBookings) {
            try {
                cancelBooking(booking.getId());
                logger.info("Expired booking cancelled: {}", booking.getBookingReference());
            } catch (Exception e) {
                logger.error("Failed to cancel expired booking: {}", booking.getBookingReference(), e);
            }
        }

        logger.info("Cleanup completed. Processed {} expired bookings", expiredBookings.size());
    }
}
