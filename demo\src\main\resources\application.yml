spring:
  application:
    name: cinema-booking-backend

  datasource:
    url: *************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
    defer-datasource-initialization: true
    open-in-view: false

  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      schema-locations: classpath:schema.sql
      continue-on-error: true

  cache:
    type: simple

  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

server:
  port: 8080
  servlet:
    context-path: /api

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  expiration: 86400000 # 24 hours in milliseconds
  refresh-expiration: 604800000 # 7 days in milliseconds

# TMDB API Configuration
external-apis:
  tmdb:
    api-key: ${TMDB_API_KEY:your_tmdb_api_key_here}
    base-url: https://api.themoviedb.org/3
    image-base-url: https://image.tmdb.org/t/p/w500
    rate-limit: 40 # requests per 10 seconds

# CORS Configuration
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# Logging Configuration
logging:
  level:
    com.cinema.booking: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/cinema-booking.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# Application specific configuration
app:
  booking:
    seat-hold-duration: 300 # 5 minutes in seconds
    booking-reference-length: 8
    max-seats-per-booking: 10

  pagination:
    default-page-size: 20
    max-page-size: 100

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

