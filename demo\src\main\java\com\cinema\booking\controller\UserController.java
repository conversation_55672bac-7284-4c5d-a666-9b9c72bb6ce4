package com.cinema.booking.controller;

import com.cinema.booking.dto.response.ApiResponse;
import com.cinema.booking.model.User;
import com.cinema.booking.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/users")
@CrossOrigin(origins = "*", maxAge = 3600)
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @GetMapping("/profile")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<User>> getUserProfile() {
        logger.info("Fetching user profile");

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User user = userService.getUserByEmail(authentication.getName());

            ApiResponse<User> response = ApiResponse.success("User profile retrieved successfully", user);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch user profile", e);
            ApiResponse<User> response = ApiResponse.error("Failed to fetch user profile: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/profile")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<User>> updateUserProfile(@RequestBody User userDetails) {
        logger.info("Updating user profile");

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            User updatedUser = userService.updateUser(currentUser.getId(), userDetails);

            ApiResponse<User> response = ApiResponse.success("User profile updated successfully", updatedUser);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to update user profile", e);
            ApiResponse<User> response = ApiResponse.error("Failed to update user profile: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/password")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updatePassword(@RequestBody Map<String, String> passwordData) {
        logger.info("Updating user password");

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            String currentPassword = passwordData.get("currentPassword");
            String newPassword = passwordData.get("newPassword");

            userService.updatePassword(currentUser.getId(), currentPassword, newPassword);

            ApiResponse<Void> response = ApiResponse.success("Password updated successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to update password", e);
            ApiResponse<Void> response = ApiResponse.error("Failed to update password: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/preferences")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateUserPreferences(@RequestBody Map<String, Object> preferences) {
        logger.info("Updating user preferences");

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            String preferredGenre = (String) preferences.get("preferredGenre");
            Long preferredTheaterId = preferences.get("preferredTheaterId") != null ? 
                                    Long.valueOf(preferences.get("preferredTheaterId").toString()) : null;

            userService.updateUserPreferences(currentUser.getId(), preferredGenre, preferredTheaterId);

            ApiResponse<Void> response = ApiResponse.success("User preferences updated successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to update user preferences", e);
            ApiResponse<Void> response = ApiResponse.error("Failed to update user preferences: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/preferences")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Object>> getUserPreferences() {
        logger.info("Fetching user preferences");

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User user = userService.getUserByEmail(authentication.getName());

            Object preferences = new Object() {
                public final String preferredGenre = user.getPreferredGenre();
                public final Long preferredTheaterId = user.getPreferredTheater() != null ? 
                                                     user.getPreferredTheater().getId() : null;
                public final String preferredTheaterName = user.getPreferredTheater() != null ? 
                                                         user.getPreferredTheater().getName() : null;
            };

            ApiResponse<Object> response = ApiResponse.success("User preferences retrieved successfully", preferences);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch user preferences", e);
            ApiResponse<Object> response = ApiResponse.error("Failed to fetch user preferences: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @DeleteMapping("/account")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteUserAccount() {
        logger.info("Deleting user account");

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            userService.deleteUser(currentUser.getId());

            ApiResponse<Void> response = ApiResponse.success("User account deleted successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to delete user account", e);
            ApiResponse<Void> response = ApiResponse.error("Failed to delete user account: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
