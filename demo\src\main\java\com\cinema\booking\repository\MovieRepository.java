package com.cinema.booking.repository;

import com.cinema.booking.model.Movie;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface MovieRepository extends JpaRepository<Movie, Long> {
    
    /**
     * Find all active movies
     */
    List<Movie> findByIsActiveTrue();
    
    /**
     * Find active movies with pagination
     */
    Page<Movie> findByIsActiveTrue(Pageable pageable);
    
    /**
     * Find movies by genre
     */
    List<Movie> findByGenreAndIsActiveTrue(String genre);
    
    /**
     * Find movies by TMDB ID
     */
    Optional<Movie> findByTmdbId(Long tmdbId);
    
    /**
     * Check if movie exists by TMDB ID
     */
    boolean existsByTmdbId(Long tmdbId);
    
    /**
     * Search movies by title
     */
    @Query("SELECT m FROM Movie m WHERE LOWER(m.title) LIKE LOWER(CONCAT('%', :title, '%')) AND m.isActive = true")
    Page<Movie> searchByTitle(@Param("title") String title, Pageable pageable);
    
    /**
     * Search movies by title, genre, or director
     */
    @Query("SELECT m FROM Movie m WHERE m.isActive = true AND (" +
           "LOWER(m.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(m.genre) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(m.director) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Movie> searchMovies(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    /**
     * Find movies by release date range
     */
    @Query("SELECT m FROM Movie m WHERE m.isActive = true AND m.releaseDate BETWEEN :startDate AND :endDate")
    List<Movie> findByReleaseDateBetween(@Param("startDate") LocalDate startDate, 
                                        @Param("endDate") LocalDate endDate);
    
    /**
     * Find currently playing movies (released and active)
     */
    @Query("SELECT m FROM Movie m WHERE m.isActive = true AND m.releaseDate <= :currentDate")
    List<Movie> findCurrentlyPlaying(@Param("currentDate") LocalDate currentDate);
    
    /**
     * Find upcoming movies (not yet released)
     */
    @Query("SELECT m FROM Movie m WHERE m.isActive = true AND m.releaseDate > :currentDate")
    List<Movie> findUpcoming(@Param("currentDate") LocalDate currentDate);
    
    /**
     * Find movies with showtimes
     */
    @Query("SELECT DISTINCT m FROM Movie m JOIN m.showtimes s WHERE m.isActive = true AND s.showDate >= :fromDate")
    List<Movie> findMoviesWithShowtimes(@Param("fromDate") LocalDate fromDate);
    
    /**
     * Find featured movies (movies with most bookings)
     */
    @Query("SELECT m FROM Movie m JOIN m.showtimes s JOIN s.bookings b " +
           "WHERE m.isActive = true AND b.bookingStatus = 'CONFIRMED' " +
           "GROUP BY m.id ORDER BY COUNT(b) DESC")
    List<Movie> findFeaturedMovies(Pageable pageable);
    
    /**
     * Find all distinct genres
     */
    @Query("SELECT DISTINCT m.genre FROM Movie m WHERE m.isActive = true AND m.genre IS NOT NULL ORDER BY m.genre")
    List<String> findAllGenres();
    
    /**
     * Find movies by duration range
     */
    @Query("SELECT m FROM Movie m WHERE m.isActive = true AND m.durationMinutes BETWEEN :minDuration AND :maxDuration")
    List<Movie> findByDurationRange(@Param("minDuration") Integer minDuration, 
                                   @Param("maxDuration") Integer maxDuration);
    
    /**
     * Find movies by rating
     */
    List<Movie> findByRatingAndIsActiveTrue(String rating);
    
    /**
     * Count active movies by genre
     */
    @Query("SELECT COUNT(m) FROM Movie m WHERE m.genre = :genre AND m.isActive = true")
    long countByGenre(@Param("genre") String genre);
}
