package com.cinema.booking.dto.response;

import com.cinema.booking.model.Booking;
import com.cinema.booking.model.BookingSeat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

public class BookingResponse {
    
    private Long id;
    private String bookingReference;
    private BigDecimal totalAmount;
    private String bookingStatus;
    private String customerName;
    private String customerEmail;
    private String customerPhone;
    private LocalDateTime bookingDate;
    private ShowtimeInfo showtime;
    private List<SeatInfo> seats;
    private boolean canBeCancelled;
    
    // Nested classes for related information
    public static class ShowtimeInfo {
        private Long id;
        private String movieTitle;
        private String theaterName;
        private String showDate;
        private String showTime;
        private BigDecimal basePrice;
        
        public ShowtimeInfo(Long id, String movieTitle, String theaterName, 
                           String showDate, String showTime, BigDecimal basePrice) {
            this.id = id;
            this.movieTitle = movieTitle;
            this.theaterName = theaterName;
            this.showDate = showDate;
            this.showTime = showTime;
            this.basePrice = basePrice;
        }
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getMovieTitle() { return movieTitle; }
        public void setMovieTitle(String movieTitle) { this.movieTitle = movieTitle; }
        public String getTheaterName() { return theaterName; }
        public void setTheaterName(String theaterName) { this.theaterName = theaterName; }
        public String getShowDate() { return showDate; }
        public void setShowDate(String showDate) { this.showDate = showDate; }
        public String getShowTime() { return showTime; }
        public void setShowTime(String showTime) { this.showTime = showTime; }
        public BigDecimal getBasePrice() { return basePrice; }
        public void setBasePrice(BigDecimal basePrice) { this.basePrice = basePrice; }
    }
    
    public static class SeatInfo {
        private Long id;
        private String seatLabel;
        private String seatType;
        private BigDecimal price;
        
        public SeatInfo(Long id, String seatLabel, String seatType, BigDecimal price) {
            this.id = id;
            this.seatLabel = seatLabel;
            this.seatType = seatType;
            this.price = price;
        }
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getSeatLabel() { return seatLabel; }
        public void setSeatLabel(String seatLabel) { this.seatLabel = seatLabel; }
        public String getSeatType() { return seatType; }
        public void setSeatType(String seatType) { this.seatType = seatType; }
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
    }
    
    // Constructors
    public BookingResponse() {}
    
    public BookingResponse(Booking booking) {
        this.id = booking.getId();
        this.bookingReference = booking.getBookingReference();
        this.totalAmount = booking.getTotalAmount();
        this.bookingStatus = booking.getBookingStatus().getDisplayName();
        this.customerName = booking.getCustomerName();
        this.customerEmail = booking.getCustomerEmail();
        this.customerPhone = booking.getCustomerPhone();
        this.bookingDate = booking.getBookingDate();
        this.canBeCancelled = booking.canBeCancelled();
        
        // Set showtime information
        if (booking.getShowtime() != null) {
            this.showtime = new ShowtimeInfo(
                booking.getShowtime().getId(),
                booking.getShowtime().getMovie() != null ? booking.getShowtime().getMovie().getTitle() : "Unknown Movie",
                booking.getShowtime().getTheater() != null ? booking.getShowtime().getTheater().getName() : "Unknown Theater",
                booking.getShowtime().getDisplayDate(),
                booking.getShowtime().getDisplayTime(),
                booking.getShowtime().getBasePrice()
            );
        }
        
        // Set seat information
        if (booking.getBookingSeats() != null) {
            this.seats = booking.getBookingSeats().stream()
                .map(bookingSeat -> new SeatInfo(
                    bookingSeat.getSeat().getId(),
                    bookingSeat.getSeatLabel(),
                    bookingSeat.getSeatType(),
                    bookingSeat.getPrice()
                ))
                .collect(Collectors.toList());
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getBookingReference() {
        return bookingReference;
    }
    
    public void setBookingReference(String bookingReference) {
        this.bookingReference = bookingReference;
    }
    
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public String getBookingStatus() {
        return bookingStatus;
    }
    
    public void setBookingStatus(String bookingStatus) {
        this.bookingStatus = bookingStatus;
    }
    
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getCustomerEmail() {
        return customerEmail;
    }
    
    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }
    
    public String getCustomerPhone() {
        return customerPhone;
    }
    
    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }
    
    public LocalDateTime getBookingDate() {
        return bookingDate;
    }
    
    public void setBookingDate(LocalDateTime bookingDate) {
        this.bookingDate = bookingDate;
    }
    
    public ShowtimeInfo getShowtime() {
        return showtime;
    }
    
    public void setShowtime(ShowtimeInfo showtime) {
        this.showtime = showtime;
    }
    
    public List<SeatInfo> getSeats() {
        return seats;
    }
    
    public void setSeats(List<SeatInfo> seats) {
        this.seats = seats;
    }
    
    public boolean isCanBeCancelled() {
        return canBeCancelled;
    }
    
    public void setCanBeCancelled(boolean canBeCancelled) {
        this.canBeCancelled = canBeCancelled;
    }
    
    // Utility methods
    public int getTotalSeats() {
        return seats != null ? seats.size() : 0;
    }
}
