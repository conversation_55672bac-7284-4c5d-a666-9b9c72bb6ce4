package com.cinema.booking.repository;

import com.cinema.booking.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * Find user by email address
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Check if user exists by email
     */
    boolean existsByEmail(String email);
    
    /**
     * Find users by role
     */
    List<User> findByRole(User.Role role);
    
    /**
     * Find users by preferred genre
     */
    List<User> findByPreferredGenre(String preferredGenre);
    
    /**
     * Find users by preferred theater
     */
    @Query("SELECT u FROM User u WHERE u.preferredTheater.id = :theaterId")
    List<User> findByPreferredTheaterId(@Param("theaterId") Long theaterId);
    
    /**
     * Find users with bookings count
     */
    @Query("SELECT u FROM User u LEFT JOIN u.bookings b GROUP BY u.id HAVING COUNT(b) > :minBookings")
    List<User> findUsersWithMinimumBookings(@Param("minBookings") long minBookings);
    
    /**
     * Search users by name or email
     */
    @Query("SELECT u FROM User u WHERE " +
           "LOWER(CONCAT(u.firstName, ' ', u.lastName)) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<User> searchUsers(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    /**
     * Find users created after a specific date
     */
    @Query("SELECT u FROM User u WHERE u.createdAt >= :fromDate")
    List<User> findUsersCreatedAfter(@Param("fromDate") java.time.LocalDateTime fromDate);
    
    /**
     * Count users by role
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.role = :role")
    long countByRole(@Param("role") User.Role role);
    
    /**
     * Find active users (users with at least one booking in the last 30 days)
     */
    @Query("SELECT DISTINCT u FROM User u JOIN u.bookings b " +
           "WHERE b.bookingDate >= :thirtyDaysAgo AND b.bookingStatus != 'CANCELLED'")
    List<User> findActiveUsers(@Param("thirtyDaysAgo") java.time.LocalDateTime thirtyDaysAgo);
}
