package com.cinema.booking.dto.response;

import com.cinema.booking.model.Movie;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class MovieResponse {
    
    private Long id;
    private String title;
    private String description;
    private String genre;
    private Integer durationMinutes;
    private String durationFormatted;
    private String rating;
    private LocalDate releaseDate;
    private String posterUrl;
    private String trailerUrl;
    private String language;
    private String director;
    private String cast;
    private Boolean isActive;
    private Long tmdbId;
    private LocalDateTime createdAt;
    private boolean currentlyPlaying;
    
    // Constructors
    public MovieResponse() {}
    
    public MovieResponse(Movie movie) {
        this.id = movie.getId();
        this.title = movie.getTitle();
        this.description = movie.getDescription();
        this.genre = movie.getGenre();
        this.durationMinutes = movie.getDurationMinutes();
        this.durationFormatted = movie.getDurationFormatted();
        this.rating = movie.getRating();
        this.releaseDate = movie.getReleaseDate();
        this.posterUrl = movie.getPosterUrl();
        this.trailerUrl = movie.getTrailerUrl();
        this.language = movie.getLanguage();
        this.director = movie.getDirector();
        this.cast = movie.getCast();
        this.isActive = movie.getIsActive();
        this.tmdbId = movie.getTmdbId();
        this.createdAt = movie.getCreatedAt();
        this.currentlyPlaying = movie.isCurrentlyPlaying();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getGenre() {
        return genre;
    }
    
    public void setGenre(String genre) {
        this.genre = genre;
    }
    
    public Integer getDurationMinutes() {
        return durationMinutes;
    }
    
    public void setDurationMinutes(Integer durationMinutes) {
        this.durationMinutes = durationMinutes;
    }
    
    public String getDurationFormatted() {
        return durationFormatted;
    }
    
    public void setDurationFormatted(String durationFormatted) {
        this.durationFormatted = durationFormatted;
    }
    
    public String getRating() {
        return rating;
    }
    
    public void setRating(String rating) {
        this.rating = rating;
    }
    
    public LocalDate getReleaseDate() {
        return releaseDate;
    }
    
    public void setReleaseDate(LocalDate releaseDate) {
        this.releaseDate = releaseDate;
    }
    
    public String getPosterUrl() {
        return posterUrl;
    }
    
    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }
    
    public String getTrailerUrl() {
        return trailerUrl;
    }
    
    public void setTrailerUrl(String trailerUrl) {
        this.trailerUrl = trailerUrl;
    }
    
    public String getLanguage() {
        return language;
    }
    
    public void setLanguage(String language) {
        this.language = language;
    }
    
    public String getDirector() {
        return director;
    }
    
    public void setDirector(String director) {
        this.director = director;
    }
    
    public String getCast() {
        return cast;
    }
    
    public void setCast(String cast) {
        this.cast = cast;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Long getTmdbId() {
        return tmdbId;
    }
    
    public void setTmdbId(Long tmdbId) {
        this.tmdbId = tmdbId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public boolean isCurrentlyPlaying() {
        return currentlyPlaying;
    }
    
    public void setCurrentlyPlaying(boolean currentlyPlaying) {
        this.currentlyPlaying = currentlyPlaying;
    }
}
