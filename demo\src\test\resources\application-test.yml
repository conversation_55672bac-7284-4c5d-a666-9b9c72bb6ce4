spring:
  application:
    name: cinema-booking-backend-test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
    defer-datasource-initialization: true
    
  sql:
    init:
      mode: never
      
  h2:
    console:
      enabled: true

server:
  port: 0

# JWT Configuration for testing
jwt:
  secret: testSecretKey123456789012345678901234567890
  expiration: 86400000 # 24 hours
  refresh-expiration: 604800000 # 7 days

# TMDB API Configuration for testing
external-apis:
  tmdb:
    api-key: test_api_key
    base-url: https://api.themoviedb.org/3
    image-base-url: https://image.tmdb.org/t/p/w500
    rate-limit: 40

# CORS Configuration for testing
cors:
  allowed-origins: "*"
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# Logging Configuration for testing
logging:
  level:
    com.cinema.booking: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Application specific configuration for testing
app:
  booking:
    seat-hold-duration: 60 # 1 minute for testing
    booking-reference-length: 6
    max-seats-per-booking: 5
  
  pagination:
    default-page-size: 10
    max-page-size: 50
