package com.cinema.booking.controller;

import com.cinema.booking.dto.response.ApiResponse;
import com.cinema.booking.dto.response.MovieResponse;
import com.cinema.booking.model.Movie;
import com.cinema.booking.service.MovieService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/movies")
@CrossOrigin(origins = "*", maxAge = 3600)
public class MovieController {

    private static final Logger logger = LoggerFactory.getLogger(MovieController.class);

    @Autowired
    private MovieService movieService;

    @GetMapping
    public ResponseEntity<ApiResponse<Page<MovieResponse>>> getAllMovies(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "title") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        logger.info("Fetching all movies - page: {}, size: {}", page, size);

        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                       Sort.by(sortBy).descending() : 
                       Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            Page<Movie> movies = movieService.getAllActiveMovies(pageable);
            
            Page<MovieResponse> movieResponses = movies.map(MovieResponse::new);
            
            ApiResponse<Page<MovieResponse>> response = ApiResponse.success("Movies retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch movies", e);
            ApiResponse<Page<MovieResponse>> response = ApiResponse.error("Failed to fetch movies: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<MovieResponse>> getMovieById(@PathVariable Long id) {
        logger.info("Fetching movie with ID: {}", id);

        try {
            Movie movie = movieService.getMovieById(id);
            MovieResponse movieResponse = new MovieResponse(movie);
            
            ApiResponse<MovieResponse> response = ApiResponse.success("Movie retrieved successfully", movieResponse);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch movie with ID: {}", id, e);
            ApiResponse<MovieResponse> response = ApiResponse.error("Failed to fetch movie: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<MovieResponse>>> searchMovies(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        logger.info("Searching movies with query: {}", query);

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Movie> movies = movieService.searchMovies(query, pageable);
            
            Page<MovieResponse> movieResponses = movies.map(MovieResponse::new);
            
            ApiResponse<Page<MovieResponse>> response = ApiResponse.success("Search results retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to search movies with query: {}", query, e);
            ApiResponse<Page<MovieResponse>> response = ApiResponse.error("Failed to search movies: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/genres")
    public ResponseEntity<ApiResponse<List<String>>> getAllGenres() {
        logger.info("Fetching all movie genres");

        try {
            List<String> genres = movieService.getAllGenres();
            
            ApiResponse<List<String>> response = ApiResponse.success("Genres retrieved successfully", genres);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch genres", e);
            ApiResponse<List<String>> response = ApiResponse.error("Failed to fetch genres: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/genre/{genre}")
    public ResponseEntity<ApiResponse<List<MovieResponse>>> getMoviesByGenre(@PathVariable String genre) {
        logger.info("Fetching movies by genre: {}", genre);

        try {
            List<Movie> movies = movieService.getMoviesByGenre(genre);
            List<MovieResponse> movieResponses = movies.stream()
                    .map(MovieResponse::new)
                    .collect(Collectors.toList());
            
            ApiResponse<List<MovieResponse>> response = ApiResponse.success("Movies by genre retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch movies by genre: {}", genre, e);
            ApiResponse<List<MovieResponse>> response = ApiResponse.error("Failed to fetch movies by genre: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/featured")
    public ResponseEntity<ApiResponse<List<MovieResponse>>> getFeaturedMovies(
            @RequestParam(defaultValue = "10") int limit) {
        
        logger.info("Fetching featured movies with limit: {}", limit);

        try {
            List<Movie> movies = movieService.getFeaturedMovies(limit);
            List<MovieResponse> movieResponses = movies.stream()
                    .map(MovieResponse::new)
                    .collect(Collectors.toList());
            
            ApiResponse<List<MovieResponse>> response = ApiResponse.success("Featured movies retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch featured movies", e);
            ApiResponse<List<MovieResponse>> response = ApiResponse.error("Failed to fetch featured movies: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/now-playing")
    public ResponseEntity<ApiResponse<List<MovieResponse>>> getCurrentlyPlayingMovies() {
        logger.info("Fetching currently playing movies");

        try {
            List<Movie> movies = movieService.getCurrentlyPlayingMovies();
            List<MovieResponse> movieResponses = movies.stream()
                    .map(MovieResponse::new)
                    .collect(Collectors.toList());
            
            ApiResponse<List<MovieResponse>> response = ApiResponse.success("Currently playing movies retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch currently playing movies", e);
            ApiResponse<List<MovieResponse>> response = ApiResponse.error("Failed to fetch currently playing movies: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/upcoming")
    public ResponseEntity<ApiResponse<List<MovieResponse>>> getUpcomingMovies() {
        logger.info("Fetching upcoming movies");

        try {
            List<Movie> movies = movieService.getUpcomingMovies();
            List<MovieResponse> movieResponses = movies.stream()
                    .map(MovieResponse::new)
                    .collect(Collectors.toList());
            
            ApiResponse<List<MovieResponse>> response = ApiResponse.success("Upcoming movies retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch upcoming movies", e);
            ApiResponse<List<MovieResponse>> response = ApiResponse.error("Failed to fetch upcoming movies: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/with-showtimes")
    public ResponseEntity<ApiResponse<List<MovieResponse>>> getMoviesWithShowtimes() {
        logger.info("Fetching movies with showtimes");

        try {
            List<Movie> movies = movieService.getMoviesWithShowtimes();
            List<MovieResponse> movieResponses = movies.stream()
                    .map(MovieResponse::new)
                    .collect(Collectors.toList());
            
            ApiResponse<List<MovieResponse>> response = ApiResponse.success("Movies with showtimes retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch movies with showtimes", e);
            ApiResponse<List<MovieResponse>> response = ApiResponse.error("Failed to fetch movies with showtimes: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/rating/{rating}")
    public ResponseEntity<ApiResponse<List<MovieResponse>>> getMoviesByRating(@PathVariable String rating) {
        logger.info("Fetching movies by rating: {}", rating);

        try {
            List<Movie> movies = movieService.getMoviesByRating(rating);
            List<MovieResponse> movieResponses = movies.stream()
                    .map(MovieResponse::new)
                    .collect(Collectors.toList());
            
            ApiResponse<List<MovieResponse>> response = ApiResponse.success("Movies by rating retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch movies by rating: {}", rating, e);
            ApiResponse<List<MovieResponse>> response = ApiResponse.error("Failed to fetch movies by rating: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/duration")
    public ResponseEntity<ApiResponse<List<MovieResponse>>> getMoviesByDuration(
            @RequestParam Integer minDuration,
            @RequestParam Integer maxDuration) {
        
        logger.info("Fetching movies by duration range: {} - {}", minDuration, maxDuration);

        try {
            List<Movie> movies = movieService.getMoviesByDurationRange(minDuration, maxDuration);
            List<MovieResponse> movieResponses = movies.stream()
                    .map(MovieResponse::new)
                    .collect(Collectors.toList());
            
            ApiResponse<List<MovieResponse>> response = ApiResponse.success("Movies by duration retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch movies by duration range", e);
            ApiResponse<List<MovieResponse>> response = ApiResponse.error("Failed to fetch movies by duration: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/release-date")
    public ResponseEntity<ApiResponse<List<MovieResponse>>> getMoviesByReleaseDate(
            @RequestParam String startDate,
            @RequestParam String endDate) {
        
        logger.info("Fetching movies by release date range: {} - {}", startDate, endDate);

        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            List<Movie> movies = movieService.getMoviesByReleaseDateRange(start, end);
            List<MovieResponse> movieResponses = movies.stream()
                    .map(MovieResponse::new)
                    .collect(Collectors.toList());
            
            ApiResponse<List<MovieResponse>> response = ApiResponse.success("Movies by release date retrieved successfully", movieResponses);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch movies by release date range", e);
            ApiResponse<List<MovieResponse>> response = ApiResponse.error("Failed to fetch movies by release date: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
