package com.cinema.booking.controller;

import com.cinema.booking.dto.request.SeatSelectionRequest;
import com.cinema.booking.dto.response.ApiResponse;
import com.cinema.booking.model.Seat;
import com.cinema.booking.model.User;
import com.cinema.booking.service.SeatService;
import com.cinema.booking.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/seats")
@CrossOrigin(origins = "*", maxAge = 3600)
public class SeatController {

    private static final Logger logger = LoggerFactory.getLogger(SeatController.class);

    @Autowired
    private SeatService seatService;

    @Autowired
    private UserService userService;

    @GetMapping("/theater/{theaterId}")
    public ResponseEntity<ApiResponse<List<Seat>>> getSeatsByTheater(@PathVariable Long theaterId) {
        logger.info("Fetching seats for theater ID: {}", theaterId);

        try {
            List<Seat> seats = seatService.getSeatsByTheater(theaterId);
            ApiResponse<List<Seat>> response = ApiResponse.success("Theater seats retrieved successfully", seats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch seats for theater ID: {}", theaterId, e);
            ApiResponse<List<Seat>> response = ApiResponse.error("Failed to fetch theater seats: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/theater/{theaterId}/layout")
    public ResponseEntity<ApiResponse<Map<String, List<Seat>>>> getSeatLayout(@PathVariable Long theaterId) {
        logger.info("Fetching seat layout for theater ID: {}", theaterId);

        try {
            Map<String, List<Seat>> seatLayout = seatService.getSeatLayoutByRow(theaterId);
            ApiResponse<Map<String, List<Seat>>> response = ApiResponse.success("Seat layout retrieved successfully", seatLayout);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch seat layout for theater ID: {}", theaterId, e);
            ApiResponse<Map<String, List<Seat>>> response = ApiResponse.error("Failed to fetch seat layout: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/showtime/{showtimeId}")
    public ResponseEntity<ApiResponse<List<Seat>>> getAvailableSeatsForShowtime(@PathVariable Long showtimeId) {
        logger.info("Fetching available seats for showtime ID: {}", showtimeId);

        try {
            List<Seat> availableSeats = seatService.getAvailableSeatsForShowtime(showtimeId);
            ApiResponse<List<Seat>> response = ApiResponse.success("Available seats retrieved successfully", availableSeats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch available seats for showtime ID: {}", showtimeId, e);
            ApiResponse<List<Seat>> response = ApiResponse.error("Failed to fetch available seats: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/showtime/{showtimeId}/booked")
    public ResponseEntity<ApiResponse<List<Seat>>> getBookedSeatsForShowtime(@PathVariable Long showtimeId) {
        logger.info("Fetching booked seats for showtime ID: {}", showtimeId);

        try {
            List<Seat> bookedSeats = seatService.getBookedSeatsForShowtime(showtimeId);
            ApiResponse<List<Seat>> response = ApiResponse.success("Booked seats retrieved successfully", bookedSeats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch booked seats for showtime ID: {}", showtimeId, e);
            ApiResponse<List<Seat>> response = ApiResponse.error("Failed to fetch booked seats: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/theater/{theaterId}/type/{seatType}")
    public ResponseEntity<ApiResponse<List<Seat>>> getSeatsByType(
            @PathVariable Long theaterId, 
            @PathVariable String seatType) {
        
        logger.info("Fetching seats by type {} for theater ID: {}", seatType, theaterId);

        try {
            Seat.SeatType type = Seat.SeatType.valueOf(seatType.toUpperCase());
            List<Seat> seats = seatService.getSeatsByTheaterAndType(theaterId, type);
            ApiResponse<List<Seat>> response = ApiResponse.success("Seats by type retrieved successfully", seats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch seats by type {} for theater ID: {}", seatType, theaterId, e);
            ApiResponse<List<Seat>> response = ApiResponse.error("Failed to fetch seats by type: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/theater/{theaterId}/wheelchair-accessible")
    public ResponseEntity<ApiResponse<List<Seat>>> getWheelchairAccessibleSeats(@PathVariable Long theaterId) {
        logger.info("Fetching wheelchair accessible seats for theater ID: {}", theaterId);

        try {
            List<Seat> seats = seatService.getWheelchairAccessibleSeats(theaterId);
            ApiResponse<List<Seat>> response = ApiResponse.success("Wheelchair accessible seats retrieved successfully", seats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch wheelchair accessible seats for theater ID: {}", theaterId, e);
            ApiResponse<List<Seat>> response = ApiResponse.error("Failed to fetch wheelchair accessible seats: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/theater/{theaterId}/premium")
    public ResponseEntity<ApiResponse<List<Seat>>> getPremiumSeats(@PathVariable Long theaterId) {
        logger.info("Fetching premium seats for theater ID: {}", theaterId);

        try {
            List<Seat> seats = seatService.getPremiumSeats(theaterId);
            ApiResponse<List<Seat>> response = ApiResponse.success("Premium seats retrieved successfully", seats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch premium seats for theater ID: {}", theaterId, e);
            ApiResponse<List<Seat>> response = ApiResponse.error("Failed to fetch premium seats: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/theater/{theaterId}/row/{rowNumber}")
    public ResponseEntity<ApiResponse<List<Seat>>> getSeatsByRow(
            @PathVariable Long theaterId, 
            @PathVariable String rowNumber) {
        
        logger.info("Fetching seats in row {} for theater ID: {}", rowNumber, theaterId);

        try {
            List<Seat> seats = seatService.getSeatsByRow(theaterId, rowNumber);
            ApiResponse<List<Seat>> response = ApiResponse.success("Seats by row retrieved successfully", seats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch seats in row {} for theater ID: {}", rowNumber, theaterId, e);
            ApiResponse<List<Seat>> response = ApiResponse.error("Failed to fetch seats by row: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/theater/{theaterId}/price-range")
    public ResponseEntity<ApiResponse<List<Seat>>> getSeatsByPriceRange(
            @PathVariable Long theaterId,
            @RequestParam BigDecimal minPrice,
            @RequestParam BigDecimal maxPrice) {
        
        logger.info("Fetching seats by price range ${}-${} for theater ID: {}", minPrice, maxPrice, theaterId);

        try {
            List<Seat> seats = seatService.getSeatsByPriceRange(theaterId, minPrice, maxPrice);
            ApiResponse<List<Seat>> response = ApiResponse.success("Seats by price range retrieved successfully", seats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch seats by price range for theater ID: {}", theaterId, e);
            ApiResponse<List<Seat>> response = ApiResponse.error("Failed to fetch seats by price range: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/hold")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Boolean>> holdSeats(@Valid @RequestBody SeatSelectionRequest request) {
        logger.info("Holding seats for showtime ID: {}", request.getShowtimeId());

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            boolean success = seatService.holdSeats(request, currentUser.getId().toString());
            
            ApiResponse<Boolean> response = ApiResponse.success("Seats held successfully", success);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to hold seats for showtime ID: {}", request.getShowtimeId(), e);
            ApiResponse<Boolean> response = ApiResponse.error("Failed to hold seats: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/release")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Boolean>> releaseSeats(@Valid @RequestBody SeatSelectionRequest request) {
        logger.info("Releasing seats for showtime ID: {}", request.getShowtimeId());

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.getUserByEmail(authentication.getName());

            boolean success = seatService.releaseSeats(request, currentUser.getId().toString());
            
            ApiResponse<Boolean> response = ApiResponse.success("Seats released successfully", success);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to release seats for showtime ID: {}", request.getShowtimeId(), e);
            ApiResponse<Boolean> response = ApiResponse.error("Failed to release seats: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/theater/{theaterId}/rows")
    public ResponseEntity<ApiResponse<List<String>>> getRowNumbers(@PathVariable Long theaterId) {
        logger.info("Fetching row numbers for theater ID: {}", theaterId);

        try {
            List<String> rowNumbers = seatService.getRowNumbers(theaterId);
            ApiResponse<List<String>> response = ApiResponse.success("Row numbers retrieved successfully", rowNumbers);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch row numbers for theater ID: {}", theaterId, e);
            ApiResponse<List<String>> response = ApiResponse.error("Failed to fetch row numbers: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/showtime/{showtimeId}/count")
    public ResponseEntity<ApiResponse<Object>> getSeatCounts(@PathVariable Long showtimeId) {
        logger.info("Fetching seat counts for showtime ID: {}", showtimeId);

        try {
            long availableCount = seatService.getAvailableSeatCount(showtimeId);
            
            Object counts = new Object() {
                public final long available = availableCount;
                public final String status = availableCount > 0 ? "Available" : "Sold Out";
            };
            
            ApiResponse<Object> response = ApiResponse.success("Seat counts retrieved successfully", counts);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch seat counts for showtime ID: {}", showtimeId, e);
            ApiResponse<Object> response = ApiResponse.error("Failed to fetch seat counts: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
