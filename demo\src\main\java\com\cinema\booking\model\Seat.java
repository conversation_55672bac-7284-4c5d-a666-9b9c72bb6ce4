package com.cinema.booking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "seats", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"theater_id", "row_number", "seat_number"}))
public class Seat {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "theater_id", nullable = false)
    @NotNull(message = "Theater is required")
    private Theater theater;
    
    @Column(name = "row_number", nullable = false, length = 1)
    @NotNull(message = "Row number is required")
    private String rowNumber;
    
    @Column(name = "seat_number", nullable = false)
    @NotNull(message = "Seat number is required")
    @Positive(message = "Seat number must be positive")
    private Integer seatNumber;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "seat_type", nullable = false)
    private SeatType seatType = SeatType.REGULAR;
    
    @Column(nullable = false, precision = 10, scale = 2)
    @NotNull(message = "Price is required")
    private BigDecimal price;
    
    @Column(name = "is_wheelchair_accessible", nullable = false)
    private Boolean isWheelchairAccessible = false;
    
    @OneToMany(mappedBy = "seat", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<BookingSeat> bookingSeats;
    
    public enum SeatType {
        REGULAR("Regular", 1.0),
        PREMIUM("Premium", 1.3),
        VIP("VIP", 1.6);
        
        private final String displayName;
        private final double priceMultiplier;
        
        SeatType(String displayName, double priceMultiplier) {
            this.displayName = displayName;
            this.priceMultiplier = priceMultiplier;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public double getPriceMultiplier() {
            return priceMultiplier;
        }
    }
    
    // Constructors
    public Seat() {}
    
    public Seat(Theater theater, String rowNumber, Integer seatNumber, SeatType seatType, BigDecimal price) {
        this.theater = theater;
        this.rowNumber = rowNumber;
        this.seatNumber = seatNumber;
        this.seatType = seatType;
        this.price = price;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Theater getTheater() {
        return theater;
    }
    
    public void setTheater(Theater theater) {
        this.theater = theater;
    }
    
    public String getRowNumber() {
        return rowNumber;
    }
    
    public void setRowNumber(String rowNumber) {
        this.rowNumber = rowNumber;
    }
    
    public Integer getSeatNumber() {
        return seatNumber;
    }
    
    public void setSeatNumber(Integer seatNumber) {
        this.seatNumber = seatNumber;
    }
    
    public SeatType getSeatType() {
        return seatType;
    }
    
    public void setSeatType(SeatType seatType) {
        this.seatType = seatType;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public Boolean getIsWheelchairAccessible() {
        return isWheelchairAccessible;
    }
    
    public void setIsWheelchairAccessible(Boolean isWheelchairAccessible) {
        this.isWheelchairAccessible = isWheelchairAccessible;
    }
    
    public List<BookingSeat> getBookingSeats() {
        return bookingSeats;
    }
    
    public void setBookingSeats(List<BookingSeat> bookingSeats) {
        this.bookingSeats = bookingSeats;
    }
    
    // Utility methods
    public String getSeatLabel() {
        return rowNumber + seatNumber;
    }
    
    public String getFullDescription() {
        return String.format("Seat %s (%s) - $%.2f", getSeatLabel(), seatType.getDisplayName(), price);
    }
    
    public BigDecimal calculatePrice(BigDecimal basePrice) {
        return basePrice.multiply(BigDecimal.valueOf(seatType.getPriceMultiplier()));
    }
    
    public boolean isPremium() {
        return seatType == SeatType.PREMIUM || seatType == SeatType.VIP;
    }
    
    public boolean isVIP() {
        return seatType == SeatType.VIP;
    }
}
